import { FastifyReply } from 'fastify';
import { SearchRequest, GetDomainByUrnRequest } from '../types/domainTypes.js';
import { searchDomains, getDomainByUrn } from '../services/domainService.js';
import { domainToResponse } from '../transformers/domainTransformer.js';
import { applyDomainFilters } from '../utils/domainFilters.js';

export async function domainSearchController(request: SearchRequest, reply: FastifyReply) {
  try {
    const filters = request.query;
    const response = await searchDomains(request, filters);

    // Map and filter results
    const rawResults = response.data.search.searchResults || [];
    const mappedResults = rawResults.map(result => domainToResponse(result.entity));
    const results = applyDomainFilters(mappedResults, filters);

    return reply.send({
      start: response.data.search.start,
      count: results.length,
      total: results.length,
      results
    });
  } catch (error: any) {
    request.log.error('Failed to fetch domains:', error);
    return reply.status(500).send({
      error: error.message || 'Failed to fetch domains',
      details: error.details || undefined,
    });
  }
}

export async function getDomainByUrnController(request: GetDomainByUrnRequest, reply: FastifyReply) {
  const { urn } = request.query;
  try {
    const domain = await getDomainByUrn(request, urn);
    if (!domain) {
      return reply.status(404).send({ error: 'Domain not found' });
    }
    return reply.send({
      urn: domain.urn,
      name: domain.properties?.name || '',
      description: domain.properties?.description || ''
    });
  } catch (error: any) {
    request.log.error('Failed to fetch domain by URN:', error);
    return reply.status(500).send({
      error: error.message || 'Failed to fetch domain',
      details: error.details || undefined,
    });
  }
} 