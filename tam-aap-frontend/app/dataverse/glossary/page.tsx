"use client";

import { useState, useEffect } from "react";
import { fetchGlossaryItems } from "@/lib/api";
import { GlossaryItem, GlossaryItemsQueryParams } from "@/types/dataverse";
import HeroSection from "@/components/dataverse/hero-section";
import AlphabetNav from "@/components/glossary/alphabet-nav";
import DepartmentSidebar from "@/components/dataverse/department-sidebar";
import GlossaryTermList from "@/components/glossary/glossary-term-list";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbSeparator,
  BreadcrumbPage,
  BreadcrumbList,
} from "@/components/ui/breadcrumb";

const ALL = "All";

export default function GlossaryPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [glossaryItems, setGlossaryItems] = useState<GlossaryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeLetter, setActiveLetter] = useState<string>(ALL);
  const [activeDomain, setActiveDomain] = useState<string | null>(ALL);
  const [sidebarExpanded, setSidebarExpanded] = useState(true);

  // TODO: Use react-query or SWR for data fetching
  useEffect(() => {
    loadGlossaryItems();
  }, [searchTerm, activeLetter, activeDomain]); //eslint-disable-line react-hooks/exhaustive-deps

  const loadGlossaryItems = async () => {
    setLoading(true);
    try {
      const params: GlossaryItemsQueryParams = {};

      if (searchTerm) {
        params.search = searchTerm;
      }

      if (activeDomain && activeDomain !== ALL) {
        params.domain = activeDomain;
      }

      if (activeLetter && activeLetter !== ALL) {
        params.initialLetter = activeLetter;
      }

      const response = await fetchGlossaryItems(params);
      setGlossaryItems(response.results);
    } catch (error) {
      console.error("Failed to load glossary items:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
  };

  const handleLetterClick = (letter: string) => {
    setActiveLetter(letter);
  };

  const handleDepartmentClick = (dept: string) => {
    setActiveDomain(activeDomain === dept ? ALL : dept);
  };

  const toggleSidebar = () => {
    setSidebarExpanded(!sidebarExpanded);
  };

  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="container mx-auto px-6 py-2">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Glossary</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      <HeroSection
        title="Speak the language of the airport community"
        description="Explore your ultimate guide to key terms and concepts in aviation. Bridge teams with a unified glossary for seamless communication and operational excellence."
        searchPlaceholder="Search Airport Glossary"
        onSearchChange={handleSearchChange}
        searchTerm={searchTerm}
        backgroundImage="/slides/slide2.jpg"
      />

      <div className="container mx-auto px-6 mt-6">
        <div className="flex flex-col md:flex-row gap-6">
          <DepartmentSidebar
            expanded={sidebarExpanded}
            toggleSidebar={toggleSidebar}
            departments={[
              { name: "Airfield" },
              { name: "Baggage" },
              { name: "Commercial" },
              { name: "Facilities Management" },
              { name: "Finance" },
              { name: "Flight Operations" },
              { name: "Landside" },
              { name: "Maintenance" },
              { name: "Passenger" },
              { name: "Regulatory & Legal" },
              { name: "Safety & Compliance" },
              { name: "Security" },
              { name: "Sustainability" },
              { name: "Technology" },
              { name: "Terminal" },
            ]}
            activeDepartment={activeDomain}
            onDepartmentClick={handleDepartmentClick}
          />

          <div className="flex-1">
            <div>
              <AlphabetNav
                activeLetter={activeLetter}
                onLetterClick={handleLetterClick}
              />
            </div>

            <div className="mt-6">
              {loading ? (
                <div className="flex justify-center items-center h-64">
                  <div className="text-muted-foreground">
                    Loading glossary terms...
                  </div>
                </div>
              ) : glossaryItems.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">
                    No glossary terms found.
                  </p>
                </div>
              ) : (
                <GlossaryTermList
                  items={glossaryItems}
                  searchTerm={searchTerm}
                  onViewAllClick={handleLetterClick}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
