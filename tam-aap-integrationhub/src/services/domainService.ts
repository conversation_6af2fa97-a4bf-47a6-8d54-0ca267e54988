import { FastifyRequest } from 'fastify';
import { buildDomainSearchQuery, buildGetDomainByUrnQuery } from '../utils/domainQueryBuilder.js';
import {
  SearchResponse,
  DomainEntity,
  DomainSearchResult,
  DomainSearchData,
  DomainApiResponse,
  DomainSearchFilters
} from '../types/domainTypes.js';
import { handleGraphQLErrors } from '../utils/graphqlErrorHandler.js';

/**
 * Fetches all domain results by paginating through the DataHub API
 * @param request The Fastify request object with DataHub client
 * @param queryText The search query text
 * @returns Array of domain search results
 */
async function fetchAllDomainResults(request: FastifyRequest, queryText: string): Promise<DomainSearchResult[]> {
  let allResults: DomainSearchResult[] = [];
  let pageStart = 0;
  const pageSize = 1000;
  while (true) {
    const query = buildDomainSearchQuery(queryText, pageStart, pageSize);
    const response = await request.datahub.query<DomainSearchData>(query);
    handleGraphQLErrors(response, `fetching domain results (page ${pageStart / pageSize + 1})`);

    const searchResults = response.data.search.searchResults || [];
    allResults = allResults.concat(searchResults);
    if (allResults.length >= response.data.search.total || searchResults.length === 0) break;
    pageStart += pageSize;
  }
  return allResults;
}

/**
 * Searches for domains in DataHub with the given filters
 * @param request The Fastify request object with DataHub client
 * @param filters The search filters to apply
 * @returns Search response with filtered results
 */
export async function searchDomains(
  request: FastifyRequest,
  filters: DomainSearchFilters
): Promise<SearchResponse> {
  const { start, count, search: name } = filters;
  const queryText = name && typeof name === 'string' && name.length > 0 ? name : '*';

  if (typeof count !== 'number') {
    // Use the helper to fetch all results
    const allResults = await fetchAllDomainResults(request, queryText);
    // Build a response object similar to the original
    return {
      data: {
        search: {
          start: 0,
          count: allResults.length,
          total: allResults.length,
          searchResults: allResults
        }
      }
    };
  }

  const query = buildDomainSearchQuery(
    queryText,
    typeof start === 'number' ? start : 0,
    /* istanbul ignore next */ 
    typeof count === 'number' ? count : 1000
  );

  const response = await request.datahub.query<DomainSearchData>(query);
  handleGraphQLErrors(response, `searching domains with query "${queryText}"`);

  // Just return the raw results; filtering will be done in the controller
  const searchResponse: SearchResponse = {
    data: {
      search: {
        start: response.data.search.start,
        count: response.data.search.count,
        total: response.data.search.total,
        searchResults: response.data.search.searchResults || []
      }
    }
  };

  return searchResponse;
}

/**
 * Fetches a domain by its URN
 * @param request The Fastify request object with DataHub client
 * @param urn The URN of the domain to fetch
 * @returns The domain entity
 */
export async function getDomainByUrn(request: FastifyRequest, urn: string): Promise<DomainEntity> {
  const query = buildGetDomainByUrnQuery(urn);
  const response = await request.datahub.query<DomainApiResponse>(query);
  handleGraphQLErrors(response, `fetching domain with URN "${urn}"`);
  return response.data.domain;
}