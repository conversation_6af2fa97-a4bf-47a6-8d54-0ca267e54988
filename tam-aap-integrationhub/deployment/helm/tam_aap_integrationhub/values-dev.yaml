replicaCount: 1

annotations:
  rollme: "{{ now | date \"20060102150405\" }}"

image:
  repository: 692859917251.dkr.ecr.eu-north-1.amazonaws.com/tam-app-integrationhub
  tag: latest
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  port: 3010

ingress:
  enabled: true
  className: ""
  annotations: {}
  hosts:
    - host: integrationhub.127.0.0.1.nip.io
      paths:
        - path: /
          pathType: Prefix
  tls: []
  
env:
  NODE_ENV: development
  INTEGRATION_HUB_PORT: 3010
  INTEGRATION_HUB_HOST: 0.0.0.0
  DATAHUB_PORT: 8080
  DATAHUB_HOST: new-datahub-datahub-gms.datahub.svc.cluster.local
  DATAHUB_SECURE: "true"

resources: {}
#  limits:
#    cpu: 100m
#    memory: 128Mi
#  requests:
#    cpu: 100m
#    memory: 128Mi

livenessProbe:
  enabled: true
  path: /health
  port: 3010

readinessProbe:
  enabled: true
  path: /health
  port: 3010

nodeSelector: {}
tolerations: []
affinity: {} 


imagePullSecrets:

# Secret for DataHub token
# Set to the name of the Kubernetes secret and key containing the DATAHUB_TOKEN
# Example:
# datahubTokenSecret:
#   name: my-datahub-token-secret
#   key: DATAHUB_TOKEN

datahubTokenSecret:
  name: datahub-token
  key: DATAHUB_TOKEN