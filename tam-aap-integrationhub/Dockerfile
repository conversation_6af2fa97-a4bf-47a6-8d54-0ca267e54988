# --- Builder stage ---
FROM node:23-alpine AS builder
WORKDIR /usr/src/app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

# --- Production stage ---
FROM node:23-alpine
WORKDIR /usr/src/app

# Copy only necessary files
COPY package*.json ./
RUN npm ci --omit=dev --ignore-scripts && \
    npm cache clean --force && \
    rm -rf /root/.npm

# Copy only the built application
COPY --from=builder /usr/src/app/dist ./dist

# Create non-root user
RUN addgroup -S appgroup && adduser -S appuser -G appgroup && \
    chown -R appuser:appgroup /usr/src/app

USER appuser

EXPOSE 3010
ENV INTEGRATION_HUB_PORT=3010 \
    INTEGRATION_HUB_HOST=0.0.0.0 \
    DATAHUB_PORT=8080 \
    DATAHUB_HOST=host.docker.internal \
    DATAHUB_SECURE=false \
    NODE_ENV=production

CMD ["npm", "start"]