import { FastifyCorsOptions } from '@fastify/cors';

if (!process.env.CORS_ALLOWED_ORIGINS) {
  throw new Error('CORS_ALLOWED_ORIGINS environment variable must be set (comma-separated list of allowed origins)');
}

const allowedOrigins = process.env.CORS_ALLOWED_ORIGINS.split(',').map(origin => origin.trim());

export const corsOptions: FastifyCorsOptions = {
  origin: (origin, cb) => {
    // Allow requests with no origin (like mobile apps, curl, etc)
    if (!origin) {
      return cb(null, true);
    }

    // Check if the origin matches any of the allowed origins
    const isAllowed = allowedOrigins.some(allowedOrigin => {
      // Remove protocol and port for comparison
      const cleanOrigin = origin.replace(/^https?:\/\//, '').replace(/:\d+$/, '');
      const cleanAllowed = allowedOrigin.replace(/^https?:\/\//, '').replace(/:\d+$/, '');
      return cleanOrigin === cleanAllowed;
    });

    if (isAllowed) {
      return cb(null, true);
    }

    console.log('CORS blocked request from origin:', origin);
    console.log('Allowed origins:', allowedOrigins);
    cb(new Error('Not allowed by CORS'), false);
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin'
  ],
  exposedHeaders: ['Content-Range', 'X-Content-Range'],
  credentials: true,
  maxAge: 86400, // 24 hours
  preflightContinue: false,
  optionsSuccessStatus: 204
}; 