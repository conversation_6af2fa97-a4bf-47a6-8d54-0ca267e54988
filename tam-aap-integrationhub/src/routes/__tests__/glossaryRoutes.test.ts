import fastify, { FastifyInstance } from 'fastify';
import glossaryRoutes from '../glossaryRoutes.js';

describe('glossaryRoutes', () => {
  let app: FastifyInstance;
  beforeAll(async () => {
    app = fastify();
    app.register(glossaryRoutes);
    await app.ready();
  });
  afterAll(() => app.close());

  it('POST /dh-glossary returns 200', async () => {
    // You may want to mock the controller for a real test
    const res = await app.inject({ method: 'POST', url: '/dh-glossary', payload: {} });
    expect([200, 400, 500]).toContain(res.statusCode);
  });

  it('GET /glossary/urn returns 400 for missing urn', async () => {
    const res = await app.inject({ method: 'GET', url: '/glossary/urn' });
    expect(res.statusCode).toBe(400);
  });

  it('GET /glossary/urn returns 200 for valid urn (mocked)', async () => {
    // You may want to mock the controller for a real test
    const res = await app.inject({ method: 'GET', url: '/glossary/urn?urn=test-urn' });
    expect([200, 404, 500]).toContain(res.statusCode);
  });
}); 