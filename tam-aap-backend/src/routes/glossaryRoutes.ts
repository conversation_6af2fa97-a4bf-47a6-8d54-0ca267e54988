import { FastifyPluginAsync } from 'fastify';
import { glossary<PERSON>andler, getGlossaryByIdHandler } from '../controllers/glossaryController';
import { glossaryRouteSchema, glossaryBodyValidationSchema, glossaryQuerySchema, glossaryByIdRouteSchema } from '../schemas/glossarySchema';
import { Static } from '@sinclair/typebox';
import { Type } from '@fastify/type-provider-typebox';

// Types for request body and query
export type GlossaryBody = Static<typeof glossaryBodyValidationSchema>;
export type GlossaryQuery = Static<typeof glossaryQuerySchema>;

const glossaryRoutes: FastifyPluginAsync = async (fastify) => {
  fastify.get<{
    Querystring: GlossaryQuery;
  }>('/glossary', {
    schema: glossaryRouteSchema,
    handler: glossaryHandler,
  });

  fastify.get<{
    Params: { id: string };
  }>('/glossary/:id', {
    schema: glossaryByIdRouteSchema,
    handler: getGlossaryByIdHandler,
  });
};

export default glossaryRoutes; 