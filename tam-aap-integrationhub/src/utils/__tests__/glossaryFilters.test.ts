import {
  filterByInitialLetter,
  filterByExactSearchMatch,
  filterByCustomProperties,
  glossaryFilterRegistry,
  applyGlossaryFilters
} from '../glossaryFilters.js';

describe('glossaryFilters', () => {
  const sampleResults = [
    { urn: 'urn:1', name: 'Alpha', description: 'First', customProperties: [{ key: 'foo', value: 'bar' }] },
    { urn: 'urn:2', name: 'Beta', description: 'Second', customProperties: [{ key: 'foo', value: 'baz' }] },
    { urn: 'urn:3', name: '<PERSON>', description: 'Third', customProperties: [{ key: 'bar', value: 'baz' }] },
    { urn: 'urn:4', name: 'alpha', description: 'Lowercase', customProperties: [{ key: 'foo', value: 'bar' }] },
    { urn: 'urn:5', name: '<PERSON>', description: '', customProperties: [] },
  ];

  describe('filterByInitialLetter', () => {
    it('filters by initial letter (case-insensitive)', () => {
      expect(filterByInitialLetter(sampleResults, 'A').map(r => r.name)).toEqual(['Alpha', 'alpha']);
      expect(filterByInitialLetter(sampleResults, 'B').map(r => r.name)).toEqual(['Beta']);
    });
    it('returns all if no initialLetter or not a string', () => {
      expect(filterByInitialLetter(sampleResults, '')).toEqual(sampleResults);
      expect(filterByInitialLetter(sampleResults, null as any)).toEqual(sampleResults);
      expect(filterByInitialLetter(sampleResults, 123 as any)).toEqual(sampleResults);
    });
  });

  describe('filterByExactSearchMatch', () => {
    it('filters by include search match in name or description', () => {
      expect(filterByExactSearchMatch(sampleResults, true, { search: 'alpha' }).map(r => r.name)).toEqual(['Alpha', 'alpha']);
      expect(filterByExactSearchMatch(sampleResults, true, { search: 'first' }).map(r => r.name)).toEqual(['Alpha']);
    });
    it('returns all if value is false or search is missing/not string', () => {
      expect(filterByExactSearchMatch(sampleResults, false, { search: 'alpha' })).toEqual(sampleResults);
      expect(filterByExactSearchMatch(sampleResults, true, {})).toEqual(sampleResults);
      expect(filterByExactSearchMatch(sampleResults, true, { search: 123 })).toEqual(sampleResults);
    });
  });

  describe('filterByCustomProperties', () => {
    it('filters by customProperties (all must match)', () => {
      expect(filterByCustomProperties(sampleResults, [{ key: 'foo', value: 'bar' }]).map(r => r.name)).toEqual(['Alpha', 'alpha']);
      expect(filterByCustomProperties(sampleResults, [{ key: 'bar', value: 'baz' }]).map(r => r.name)).toEqual(['Gamma']);
      expect(filterByCustomProperties(sampleResults, [{ key: 'foo', value: 'bar' }, { key: 'bar', value: 'baz' }])).toEqual([]);
    });
    it('returns all if customProperties is not array or empty', () => {
      expect(filterByCustomProperties(sampleResults, null as any)).toEqual(sampleResults);
      expect(filterByCustomProperties(sampleResults, [])).toEqual(sampleResults);
      expect(filterByCustomProperties(sampleResults, 'not-an-array' as any)).toEqual(sampleResults);
    });
  });

  describe('glossaryFilterRegistry', () => {
    it('should call the correct filter', () => {
      expect(glossaryFilterRegistry.initialLetter(sampleResults, 'A', {} as any).map(r => r.name)).toEqual(['Alpha', 'alpha']);
      expect(glossaryFilterRegistry.exactSearchMatch(sampleResults, true, { search: 'alpha' }).map(r => r.name)).toEqual(['Alpha', 'alpha']);
      expect(glossaryFilterRegistry.customProperties(sampleResults, [{ key: 'foo', value: 'bar' }], {} as any).map(r => r.name)).toEqual(['Alpha', 'alpha']);
    });
  });

  describe('applyGlossaryFilters', () => {
    it('filters by substring search (case-insensitive)', () => {
      expect(applyGlossaryFilters(sampleResults, { search: 'alp' }).map(r => r.name)).toEqual(['Alpha', 'alpha']);
      expect(applyGlossaryFilters(sampleResults, { search: 'SECOND' }).map(r => r.name)).toEqual(['Beta']);
    });
    it('filters by initialLetter', () => {
      expect(applyGlossaryFilters(sampleResults, { initialLetter: 'G' }).map(r => r.name)).toEqual(['Gamma']);
    });
    it('filters by exactSearchMatch', () => {
      expect(applyGlossaryFilters(sampleResults, { search: 'alpha', exactSearchMatch: true }).map(r => r.name)).toEqual(['Alpha', 'alpha']);
      expect(applyGlossaryFilters(sampleResults, { search: 'third', exactSearchMatch: true }).map(r => r.name)).toEqual(['Gamma']);
    });
    it('filters by customProperties', () => {
      expect(applyGlossaryFilters(sampleResults, { customProperties: [{ key: 'foo', value: 'bar' }] }).map(r => r.name)).toEqual(['Alpha', 'alpha']);
    });
    it('returns all if no filters', () => {
      expect(applyGlossaryFilters(sampleResults, {})).toEqual(sampleResults.sort((a, b) => a.name.localeCompare(b.name, undefined, { sensitivity: 'base' })));
    });
    it('sorts results alphabetically by name (case-insensitive)', () => {
      const shuffled = [sampleResults[2], sampleResults[1], sampleResults[0], sampleResults[4], sampleResults[3]];
      expect(applyGlossaryFilters(shuffled, {}).map(r => r.name)).toEqual(['alpha', 'Alpha', 'Beta', 'Delta', 'Gamma']);
    });
    it('handles edge cases: missing name/description/customProperties', () => {
      const edgeResults = [
        { urn: 'urn:6', name: '', description: '', customProperties: [] },
        { urn: 'urn:7', name: undefined, description: undefined, customProperties: undefined },
        { urn: 'urn:8', name: 'Alpha', description: null, customProperties: null },
      ];
      expect(() => applyGlossaryFilters(edgeResults as any, { search: 'alpha' })).not.toThrow();
      expect(() => applyGlossaryFilters(edgeResults as any, { initialLetter: 'A' })).not.toThrow();
      expect(() => applyGlossaryFilters(edgeResults as any, { exactSearchMatch: true, search: 'alpha' })).not.toThrow();
      expect(() => applyGlossaryFilters(edgeResults as any, { customProperties: [{ key: 'foo', value: 'bar' }] })).not.toThrow();
    });
  });
});