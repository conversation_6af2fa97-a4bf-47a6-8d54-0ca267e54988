import { Static } from '@sinclair/typebox';
import { GlossaryTerm, GlossaryTermEntity } from '../types/glossaryTypes.js';

export function glossaryTermToResponse(entity: GlossaryTermEntity): Static<typeof GlossaryTerm> {
  return {
    urn: entity.urn,
    name: entity.glossaryTermInfo.name,
    description: entity.glossaryTermInfo.description,
    customProperties: entity.glossaryTermInfo.customProperties
  };
} 