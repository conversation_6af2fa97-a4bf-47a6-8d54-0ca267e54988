import { Type } from '@sinclair/typebox'
import { FastifyRequest } from 'fastify';
import { Static } from '@sinclair/typebox';

// Common Types
export const PaginationQuery = Type.Object({
  start: Type.Optional(Type.Number({ minimum: 0, description: 'The index of the first result to return (pagination offset).' })),
  count: Type.Optional(Type.Number({ minimum: 1, description: 'The maximum number of results to return (pagination limit).' }))
})

// Request Types
export const DomainSearchQuery = Type.Object({
  search: Type.Optional(Type.String({
    description: 'Full-text search term to match against domain name and description. This will search for partial matches in both the name and description fields.'
  })),
  initialLetter: Type.Optional(Type.String({
    minLength: 1,
    maxLength: 1,
    description: 'Filter domains whose name starts with this single letter. Case-insensitive. Example: "F" will match "Flight Operations".'
  })),
  exactSearchMatch: Type.Optional(Type.Boolean({
    description: 'If true, only return domains whose name exactly matches the search term (case-insensitive). If false, partial matches are allowed.'
  }))
}, {
  description: 'Query parameters for searching domains in DataHub. All fields are optional and can be combined for more specific searches.'
})

// Response Types
export const Domain = Type.Object({
  urn: Type.String(),
  name: Type.String(),
  description: Type.String()
})

export const DomainSearchResponse = Type.Object({
  start: Type.Number(),
  count: Type.Number(),
  total: Type.Number(),
  results: Type.Array(Domain)
})

// Error Response
export const ErrorResponse = Type.Object({
  error: Type.String(),
  details: Type.Optional(Type.Union([Type.String(), Type.Array(Type.String())]))
})

// Full Route Schema
export const DomainSearchSchema = {
  tags: ['Domains'],
  summary: 'Search domains in DataHub',
  description: 'Search for domains with flexible filtering options including text search, name initial, and exact matching.',
  querystring: Type.Intersect([DomainSearchQuery, PaginationQuery]),
  response: {
    200: DomainSearchResponse,
    500: ErrorResponse
  }
}

/**
 * Represents a domain entity as returned by DataHub GraphQL API
 */
export interface DomainEntity {
  urn: string;
  properties?: {
    name?: string;
    description?: string;
  };
}

/**
 * Represents a search result item from DataHub GraphQL API
 */
export interface DomainSearchResult {
  entity: DomainEntity;
}

/**
 * Represents the search response data structure from DataHub GraphQL API
 */
export interface DomainSearchData {
  search: {
    start: number;
    count: number;
    total: number;
    searchResults: DomainSearchResult[];
  };
}

/**
 * Represents a domain response object returned to clients
 */
export interface DomainResponse {
  urn: string;
  name: string;
  description: string;
}

/**
 * Represents the domain response data structure from DataHub GraphQL API
 */
export interface DomainApiResponse {
  domain: DomainEntity;
}

/**
 * Interface for domain search filters
 */
export interface DomainSearchFilters {
  start?: number;
  count?: number;
  search?: string;
  initialLetter?: string;
  exactSearchMatch?: boolean;
  [key: string]: any; // Allow additional filter properties
}

/**
 * Represents the complete search response returned to clients
 */
export interface SearchResponse {
  data: {
    search: {
      start: number;
      count: number;
      total: number;
      searchResults: DomainSearchResult[];
    };
  };
  errors?: Array<{ message: string }>;
}

export type SearchRequest = FastifyRequest<{
  Querystring: Static<typeof DomainSearchQuery> & Static<typeof PaginationQuery>;
}>;

export type GetDomainByUrnRequest = FastifyRequest<{ Querystring: { urn: string } }>;

export const DomainUrnSchema = {
  tags: ['Domains'],
  summary: 'Get domain by URN',
  description: 'Fetch a single domain by its URN.',
  querystring: {
    type: 'object',
    properties: {
      urn: { type: 'string', description: 'The URN of the domain to fetch.' }
    },
    required: ['urn']
  },
  response: {
    200: Domain,
    404: ErrorResponse,
    500: ErrorResponse
  }
};