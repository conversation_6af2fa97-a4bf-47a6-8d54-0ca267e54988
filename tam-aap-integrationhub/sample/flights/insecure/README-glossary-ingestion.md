# DataHub Glossary Ingestion: Domain Provisioning & Ingestion Guide

This guide explains how to provision a domain in DataHub (required for glossary term domain references) and how to ingest a business glossary YAML file using the DataHub CLI.

---

## 1. Provision the Domain (Required for Glossary Ingestion)

Glossary terms that reference a domain (e.g., `domain: Flight Operations`) require that the domain exists in DataHub. You can create a domain via the DataHub UI or using a GraphQL mutation.

### Option A: Create Domain via DataHub UI

1. Open the DataHub UI (typically at http://localhost:9002).
2. Go to **Govern > Domains**.
3. Click **+ New Domain**.
4. Enter the domain name (e.g., `Flight Operations`) and description.
5. Save.

### Option B: Create Domain via GraphQL

1. Open the GraphQL Playground (typically at http://localhost:8080/api/graphql).
2. Run the following mutation:

```graphql
mutation createDomain {
  createDomain(
    input: {
      name: "Flight Operations"
      description: "Domain for flight operations and related business terms."
    }
  )
}
```

- The response will include the new domain's URN.

---

## 2. Prepare Your Glossary YAML

- Ensure your glossary YAML (e.g., `tam-glossary-1.yml`) references the domain by name (e.g., `domain: Flight Operations`).
- Example snippet:
  ```yaml
  terms:
    - name: Aircraft departures
      description: The number of take-offs of aircraft.
      domain: Flight Operations
  ```

---

## 3. Create a DataHub Ingestion Recipe (Receipt)

Create a YAML file (e.g., `datahub/tam-glossary-1.dhub.yaml`) with the following content:

```yaml
source:
  type: datahub-business-glossary
  config:
    file: /absolute/path/to/tam-glossary-1.yml # Update this path as needed
    enable_auto_id: true

sink:
  type: datahub-rest
  config:
    server: 'http://localhost:8080'
```

---

## 4. Ingest the Glossary

Run the following command in your terminal:

```sh
datahub ingest -c datahub/tam-glossary-1.dhub.yaml
```

- This will ingest the glossary terms and nodes into DataHub, associating them with the provisioned domain.
- If you see errors about missing domains, ensure the domain is created and the name matches exactly.

---

## Troubleshooting

- **Domain not found:** Make sure the domain exists in DataHub before ingestion.
- **Path issues:** Use an absolute path for the glossary YAML in the ingestion recipe if you encounter file not found errors.
- **Glossary not visible:** Refresh the DataHub UI and check under "Glossary" and "Domains".

---

## References

- [DataHub Business Glossary Ingestion Docs](https://datahubproject.io/docs/generated/ingestion/sources/business-glossary/)
- [DataHub Domains Guide](https://datahubproject.io/docs/api/tutorials/domains/)
