# CHANGELOG.md

## Shall follow the format below:
```markdown
## [Number] - YYYY-MM-DD
- Description [Ticket Number](https://example.com/ticket/1234)
```

## [7] - 2025-05-24
- token base graphql auth activation and github action modification for secret [TT-156](https://info-q.atlassian.net/browse/TT-156)

## [6] - 2025-05-24
- BE: moving IntegrationHub and backend to aap-tam namespace [TT-169](https://info-q.atlassian.net/browse/TT-169)
  
## [5] - 2025-05-18
- BE: AAP IntgerationHub Postman collections [TT-129](https://info-q.atlassian.net/browse/TT-129)

## [4] - 2025-05-16
- BE: AAP IntgerationHub UnitTests [TT-129](https://info-q.atlassian.net/browse/TT-129)

## [3] - 2025-05-14
- Token based qraphql query from IntegrationHub to DataHub [TT-150](https://info-q.atlassian.net/browse/TT-150)

## [2] - 2025-05-11
- BE: AAP IntgerationHub Helm Chart [TT-129](https://info-q.atlassian.net/browse/TT-129)

## [1] - 2025-05-10
- BE: Endpoints to GET request [TT-126](https://info-q.atlassian.net/browse/TT-126)

## [0] - 2025-05-05
- BE: Add exact search functionality [TT-126](https://info-q.atlassian.net/browse/TT-126)
- BE: First draft of AAP integrationHub [TT-126](https://info-q.atlassian.net/browse/TT-126)
