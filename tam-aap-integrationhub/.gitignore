# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.env

# Logs
logs/
*.log

# OS
.DS_Store
Thumbs.db

# Docker
*.local
*.pid
docker-compose.override.yml

# VSCode
.vscode/

# Python (if venv is used for scripts)
venv/
__pycache__/
*.pyc

# Coverage
coverage/
.nyc_output/

# Build
/dist/
/build/

# Helm
helm/datahub-integration/charts/

# Misc
*.swp
*.swo


# venv
.venv/

# values-dev.yaml
values-*.yaml