import { Type, Static } from '@sinclair/typebox'
import { GraphQLResponse } from './fastifyTypes.js'

// Common Types
export const CustomProperty = Type.Object({
  key: Type.String(),
  value: Type.String()
})

export const PaginationQuery = Type.Object({
  start: Type.Optional(Type.Number({ minimum: 0, description: 'The index of the first result to return (pagination offset).' })),
  count: Type.Optional(Type.Number({ minimum: 1, description: 'The maximum number of results to return (pagination limit).' }))
})

// Request Types
export const GlossarySearchQuery = Type.Object({
  search: Type.Optional(Type.String({
    description: 'A full-text search term. Matches against both the glossary term name and description. Partial and case-insensitive matches are supported.'
  })),
  domainName: Type.Optional(Type.String({
    description: 'Restrict results to glossary terms belonging to this domain. The domain name must match exactly (case-sensitive).'
  })),
  initialLetter: Type.Optional(Type.String({
    minLength: 1,
    maxLength: 1,
    description: 'Filter glossary terms whose name starts with this single letter (case-insensitive).'
  })),
  exactSearchMatch: Type.Optional(Type.Boolean({
    description: 'If true, the search term will match as a substring in either the name or description (case-insensitive). If false or omitted, partial matches are allowed.'
  })),
  start: Type.Optional(Type.Number({
    minimum: 0,
    description: 'The index of the first result to return (pagination offset). Use in combination with "count" for paginated results. Default is 0.'
  })),
  count: Type.Optional(Type.Number({
    minimum: 1,
    description: 'The maximum number of results to return (pagination limit). If not provided, all results will be returned in batches of 1000 until all are fetched.'
  }))
}, {
  description: 'Query parameters for searching glossary terms in DataHub. All fields are optional and can be combined for more specific searches.'
})

export const GlossaryCustomPropertiesBody = Type.Object({
  customProperties: Type.Optional(Type.Array(CustomProperty, {
    description: 'List of custom property key-value pairs to filter glossary terms. Each term must match all specified properties.'
  }))
})

// Response Types
export const GlossaryTerm = Type.Object({
  urn: Type.String(),
  name: Type.String(),
  description: Type.String(),
  customProperties: Type.Array(CustomProperty)
})

export const GlossarySearchResponse = Type.Object({
  start: Type.Number(),
  count: Type.Number(),
  total: Type.Number(),
  results: Type.Array(GlossaryTerm)
})

// Error Response
export const ErrorResponse = Type.Object({
  error: Type.String(),
  details: Type.Optional(Type.Union([Type.String(), Type.Array(Type.String())]))
})

// Full Route Schema
export const GlossarySearchSchema = {
  tags: ['Glossary'],
  summary: 'Search glossary terms in DataHub',
  description: 'Search for glossary terms with flexible filtering options. All filters except customProperties are query parameters. customProperties must be sent in the POST body.',
  querystring: GlossarySearchQuery,
  body: GlossaryCustomPropertiesBody,
  response: {
    200: GlossarySearchResponse,
    500: ErrorResponse
  }
}

/**
 * Represents a glossary term entity as returned by DataHub GraphQL API
 */
export interface GlossaryTermEntity {
  urn: string;
  name: string;
  glossaryTermInfo: {
    name: string;
    description: string;
    customProperties: Static<typeof CustomProperty>[];
  };
}

/**
 * Represents a search result item from DataHub GraphQL API for glossary terms
 */
export interface GlossarySearchResult {
  entity: GlossaryTermEntity;
}

/**
 * Represents the search response data structure from DataHub GraphQL API for glossary terms
 */
export interface GlossarySearchData {
  search: {
    start: number;
    count: number;
    total: number;
    searchResults: GlossarySearchResult[];
  };
}

/**
 * Represents a glossary term response from DataHub GraphQL API
 */
export interface GlossaryTermResponse {
  glossaryTerm: GlossaryTermEntity;
}

/**
 * Represents the complete search response returned to clients for glossary terms
 */
export interface GlossarySearchResponse extends GraphQLResponse<GlossarySearchData> {}

export interface GlossarySearchFilters {
  domainName?: string;
  start?: number;
  count?: number;
  search?: string;
  initialLetter?: string;
  exactSearchMatch?: boolean;
  customProperties?: Static<typeof CustomProperty>[];
  [key: string]: any;
}

export const GlossaryUrnSchema = {
  tags: ['Glossary'],
  summary: 'Get glossary term by URN',
  description: 'Fetch a single glossary term by its URN.',
  querystring: {
    type: 'object',
    properties: {
      urn: { type: 'string', description: 'The URN of the glossary term to fetch.' }
    },
    required: ['urn']
  },
  response: {
    200: GlossaryTerm,
    404: ErrorResponse,
    500: ErrorResponse
  }
};