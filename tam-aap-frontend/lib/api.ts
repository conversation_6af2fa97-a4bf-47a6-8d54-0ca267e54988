import {
  GlossaryItem,
  GlossaryItemsQueryParams,
  GlossaryItemsResponse,
  MetricsItem,
  MetricsItemsResponse,
} from "@/types/dataverse";

// Use the proxy endpoint
const API_URL = typeof window !== 'undefined' ? window.location.origin + '/api' : '/api';

// Glossary
export async function fetchGlossaryItems(
  params: GlossaryItemsQueryParams = {}
): Promise<GlossaryItemsResponse> {
  const url = new URL(`${API_URL}/glossary`);
  // If no filters are applied, set maxItemsPerCharacters to 3
  if (!params.search && !params.domain && !params.initialLetter) {
    params.maxItemsPerCharacters = 3;
  }

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined) url.searchParams.set(key, value.toString());
  });

  const res = await fetch(url.toString());
  if (!res.ok) throw new Error("Failed to fetch glossary items");

  return res.json();
}

export async function fetchGlossaryItemById(
  id: string
): Promise<GlossaryItem | null> {
  const res = await fetch(`${API_URL}/glossary/${id}`);
  if (!res.ok) throw new Error("Failed to fetch glossary item");

  return res.json();
}

// Metrics
export async function fetchMetricsItems(
  params: {
    search?: string;
    department?: string;
    domain?: string;
  } = {}
): Promise<MetricsItemsResponse> {
  const url = new URL(`${API_URL}/metrics`);

  Object.entries(params).forEach(([key, value]) => {
    if (value) url.searchParams.set(key, value);
  });

  const res = await fetch(url.toString());
  if (!res.ok) throw new Error("Failed to fetch metrics items");

  return res.json();
}

export async function fetchMetricsItemById(
  id: string
): Promise<MetricsItem | null> {
  const res = await fetch(`${API_URL}/metrics/${id}`);
  if (!res.ok) throw new Error("Failed to fetch metrics item");

  return res.json();
}
