{"name": "tam-aap-backend", "version": "1.0.0", "main": "dist/app.js", "scripts": {"start": "node dist/app.js", "dev": "ts-node-dev --respawn --transpile-only src/app.ts", "build": "tsc", "format": "prettier --write . --loglevel debug"}, "dependencies": {"@fastify/cors": "^11.0.1", "@fastify/swagger": "^9.5.0", "@fastify/swagger-ui": "^5.2.2", "@fastify/type-provider-typebox": "^3.5.0", "axios": "^1.6.7", "dotenv": "^16.5.0", "fastify": "^5.3.2"}, "devDependencies": {"@types/node": "^20.11.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "nodemon": "^3.0.3", "pino-pretty": "^13.0.0", "prettier": "^3.5.3", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}}