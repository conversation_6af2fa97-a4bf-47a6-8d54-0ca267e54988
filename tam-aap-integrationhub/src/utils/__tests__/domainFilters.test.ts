import { filterByInitialLetter, filterByExactSearchMatch, applyDomainFilters, domainFilterRegistry } from '../domainFilters.js';

describe('domainFilters', () => {
  const mockResults = [
    { name: 'Flight Operations', description: 'Domain for flight ops' },
    { name: 'Finance', description: 'Domain for finance' },
    { name: 'test', description: '' },
    { name: 'flight operations', description: 'lowercase name' },
  ];

  describe('filterByInitialLetter', () => {
    it('filters by initial letter (case-insensitive) with flat structure', () => {
      const filtered = filterByInitialLetter(mockResults, 'F');
      expect(filtered).toEqual([
        { name: 'Flight Operations', description: 'Domain for flight ops' },
        { name: 'Finance', description: 'Domain for finance' },
        { name: 'flight operations', description: 'lowercase name' },
      ]);
    });

    it('returns all if no initialLetter provided', () => {
      expect(filterByInitialLetter(mockResults, '')).toEqual(mockResults);
    });

    it('handles invalid input gracefully', () => {
      // Test with objects that have neither entity.properties.name nor name
      const invalidResults = [
        { someOtherProperty: 'value' },
        { entity: { someOtherProperty: 'value' } },
        { entity: { properties: { someOtherProperty: 'value' } } }
      ];

      const filtered = filterByInitialLetter(invalidResults, 'F');
      expect(filtered).toEqual([]);
    });
  });

  describe('filterByExactSearchMatch', () => {
    // Create a flattened version of mockResults for filterByExactSearchMatch tests
    const flatMockResults = [
      { name: 'Flight Operations', description: 'Domain for flight ops' },
      { name: 'Finance', description: 'Domain for finance' },
      { name: 'test', description: '' },
      { name: 'flight operations', description: 'lowercase name' },
    ];

    it('filters by exact name match (case-insensitive)', () => {
      const filtered = filterByExactSearchMatch(flatMockResults, 'flight operations');
      expect(filtered).toEqual([
        { name: 'Flight Operations', description: 'Domain for flight ops' },
        { name: 'flight operations', description: 'lowercase name' },
      ]);
    });
    it('filters by exact description match (case-insensitive)', () => {
      const filtered = filterByExactSearchMatch(flatMockResults, 'domain for finance');
      expect(filtered).toEqual([
        { name: 'Finance', description: 'Domain for finance' }
      ]);
    });
    it('returns all if no searchTerm provided', () => {
      expect(filterByExactSearchMatch(flatMockResults, '')).toEqual(flatMockResults);
    });
  });

  describe('domainFilterRegistry', () => {
    it('contains initialLetter filter function', () => {
      expect(typeof domainFilterRegistry.initialLetter).toBe('function');

      // Test the filter function directly
      const filtered = domainFilterRegistry.initialLetter(mockResults, 'F', {});
      expect(filtered).toEqual([
        { name: 'Flight Operations', description: 'Domain for flight ops' },
        { name: 'Finance', description: 'Domain for finance' },
        { name: 'flight operations', description: 'lowercase name' },
      ]);
    });

    it('contains exactSearchMatch filter function', () => {
      expect(typeof domainFilterRegistry.exactSearchMatch).toBe('function');

      // Test with search parameter
      const flatResults = [
        { name: 'Flight Operations', description: 'Domain for flight ops' },
        { name: 'Finance', description: 'Domain for finance' },
      ];

      const filtered = domainFilterRegistry.exactSearchMatch(
        flatResults,
        true,
        { search: 'flight operations' }
      );

      expect(filtered).toEqual([
        { name: 'Flight Operations', description: 'Domain for flight ops' },
      ]);

      // Test without search parameter
      const noSearchFiltered = domainFilterRegistry.exactSearchMatch(
        flatResults,
        true,
        {}
      );

      expect(noSearchFiltered).toEqual(flatResults);
    });
  });

  describe('applyDomainFilters', () => {
    it('applies initialLetter filter', () => {
      const result = applyDomainFilters(mockResults, { initialLetter: 'F' });
      expect(result).toEqual([
        { name: 'Flight Operations', description: 'Domain for flight ops' },
        { name: 'Finance', description: 'Domain for finance' },
        { name: 'flight operations', description: 'lowercase name' },
      ]);
    });

    it('applies exactSearchMatch filter when search is provided', () => {
      // Create a flattened version of results for this test
      const flatResults = [
        { name: 'Flight Operations', description: 'Domain for flight ops' },
        { name: 'Finance', description: 'Domain for finance' },
      ];

      const result = applyDomainFilters(flatResults, {
        exactSearchMatch: true,
        search: 'flight operations'
      });

      expect(result).toEqual([
        { name: 'Flight Operations', description: 'Domain for flight ops' },
      ]);
    });

    it('does not apply exactSearchMatch filter when search is not provided', () => {
      // Create a flattened version of results for this test
      const flatResults = [
        { name: 'Flight Operations', description: 'Domain for flight ops' },
        { name: 'Finance', description: 'Domain for finance' },
      ];

      // Apply exactSearchMatch filter without providing a search parameter
      const result = applyDomainFilters(flatResults, {
        exactSearchMatch: true
      });

      // Should return all results unchanged since no search term was provided
      expect(result).toEqual(flatResults);
    });

    it('ignores filters that are not in the registry', () => {
      const result = applyDomainFilters(mockResults, {
        initialLetter: 'F',
        unknownFilter: 'value'
      });

      expect(result).toEqual([
        { name: 'Flight Operations', description: 'Domain for flight ops' },
        { name: 'Finance', description: 'Domain for finance' },
        { name: 'flight operations', description: 'lowercase name' },
      ]);
    });

    it('returns original results if no filters match', () => {
      const result = applyDomainFilters(mockResults, { nonExistentFilter: 'value' });
      expect(result).toEqual(mockResults);
    });
  });
});