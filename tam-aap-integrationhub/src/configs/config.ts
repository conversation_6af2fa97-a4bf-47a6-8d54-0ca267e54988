import dotenv from 'dotenv';
dotenv.config();

export const INTEGRATION_HUB_PORT: number = Number(process.env.INTEGRATION_HUB_PORT) || 3000;
export const INTEGRATION_HUB_HOST: string = process.env.INTEGRATION_HUB_HOST || '0.0.0.0';
export const DATAHUB_PORT: number = Number(process.env.DATAHUB_PORT) || 8080;
export const DATAHUB_HOST: string = process.env.DATAHUB_HOST || 'localhost';

export const DATAHUB_GRAPHQL_ENDPOINT: string = `http://${DATAHUB_HOST}:${DATAHUB_PORT}/api/graphql`;
export const DATAHUB_TOKEN: string | undefined = process.env.DATAHUB_TOKEN; 