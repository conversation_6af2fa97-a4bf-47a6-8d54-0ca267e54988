import { glossarySearchController, getGlossaryTermByUrnController } from '../glossaryController.js';
import { searchGlossaryTerms, getGlossaryTermByUrn } from '../../services/glossaryService.js';
import { glossaryTermToResponse } from '../../transformers/glossaryTransformer.js';
import { applyGlossaryFilters } from '../../utils/glossaryFilters.js';

jest.mock('../../services/glossaryService.js');
jest.mock('../../transformers/glossaryTransformer.js');
jest.mock('../../utils/glossaryFilters.js');

describe('glossaryController', () => {
  let mockReply: any;
  let mockRequest: any;

  beforeEach(() => {
    mockReply = {
      send: jest.fn().mockReturnThis(),
      status: jest.fn().mockReturnThis(),
    };
    mockRequest = {
      query: {},
      body: {},
      log: { error: jest.fn() },
    };
    jest.clearAllMocks();
  });

  describe('glossarySearchController', () => {
    it('should return paginated glossary search results', async () => {
      (searchGlossaryTerms as jest.Mock).mockResolvedValue({
        data: { search: { searchResults: [{ entity: { urn: 'urn:li:glossaryTerm:1' } }], start: 0, count: 1, total: 1 } }
      });
      (glossaryTermToResponse as jest.Mock).mockReturnValue({ urn: 'urn:li:glossaryTerm:1', name: 'Test Term' });
      (applyGlossaryFilters as jest.Mock).mockReturnValue([{ urn: 'urn:li:glossaryTerm:1', name: 'Test Term' }]);

      mockRequest.query = { start: 0, count: 1 };
      await glossarySearchController(mockRequest, mockReply);
      expect(searchGlossaryTerms).toHaveBeenCalled();
      expect(glossaryTermToResponse).toHaveBeenCalled();
      expect(applyGlossaryFilters).toHaveBeenCalled();
      expect(mockReply.send).toHaveBeenCalledWith({
        start: 0,
        count: 1,
        total: 1,
        results: [{ urn: 'urn:li:glossaryTerm:1', name: 'Test Term' }]
      });
    });

    it('should handle errors and return 500', async () => {
      (searchGlossaryTerms as jest.Mock).mockRejectedValue(new Error('fail'));
      mockRequest.query = { start: 0, count: 1 };
      await glossarySearchController(mockRequest, mockReply);
      expect(mockReply.status).toHaveBeenCalledWith(500);
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'fail', details: undefined });
    });

    it('should handle customProperties filtering and pagination', async () => {
      (searchGlossaryTerms as jest.Mock).mockResolvedValue({
        data: { search: { searchResults: [
          { entity: { urn: 'urn:li:glossaryTerm:1' } },
          { entity: { urn: 'urn:li:glossaryTerm:2' } },
          { entity: { urn: 'urn:li:glossaryTerm:3' } }
        ], start: 0, count: 3, total: 3 } }
      });
      (glossaryTermToResponse as jest.Mock).mockImplementation(entity => ({ urn: entity.urn, name: `Term ${entity.urn}` }));
      (applyGlossaryFilters as jest.Mock).mockImplementation((results, filters) => results.filter((r: any) => r.urn !== 'urn:li:glossaryTerm:2'));

      mockRequest.query = { start: 0, count: 1 };
      mockRequest.body = { customProperties: [{ key: 'foo', value: 'bar' }] };
      await glossarySearchController(mockRequest, mockReply);
      // Should paginate after filtering (filtered results: 2, paginated: 1)
      expect(searchGlossaryTerms).toHaveBeenCalledWith(mockRequest, expect.objectContaining({ count: undefined, customProperties: [{ key: 'foo', value: 'bar' }] }));
      expect(applyGlossaryFilters).toHaveBeenCalled();
      expect(mockReply.send).toHaveBeenCalledWith({
        start: 0,
        count: 1,
        total: 2,
        results: [
          { urn: 'urn:li:glossaryTerm:1', name: 'Term urn:li:glossaryTerm:1' }
        ]
      });
    });

    it('should handle when customProperties is an empty array (no special pagination)', async () => {
      (searchGlossaryTerms as jest.Mock).mockResolvedValue({
        data: { search: { searchResults: [
          { entity: { urn: 'urn:li:glossaryTerm:1' } },
          { entity: { urn: 'urn:li:glossaryTerm:2' } }
        ], start: 0, count: 2, total: 2 } }
      });
      (glossaryTermToResponse as jest.Mock).mockImplementation(entity => ({ urn: entity.urn, name: `Term ${entity.urn}` }));
      (applyGlossaryFilters as jest.Mock).mockImplementation((results: any[]) => results);
      mockRequest.query = { start: 0, count: 2 };
      mockRequest.body = { customProperties: [] };
      await glossarySearchController(mockRequest, mockReply);
      expect(mockReply.send).toHaveBeenCalledWith({
        start: 0,
        count: 2,
        total: 2,
        results: [
          { urn: 'urn:li:glossaryTerm:1', name: 'Term urn:li:glossaryTerm:1' },
          { urn: 'urn:li:glossaryTerm:2', name: 'Term urn:li:glossaryTerm:2' }
        ]
      });
    });

    it('should handle when customProperties is not an array (no special pagination)', async () => {
      (searchGlossaryTerms as jest.Mock).mockResolvedValue({
        data: { search: { searchResults: [
          { entity: { urn: 'urn:li:glossaryTerm:1' } }
        ], start: 0, count: 1, total: 1 } }
      });
      (glossaryTermToResponse as jest.Mock).mockImplementation(entity => ({ urn: entity.urn, name: `Term ${entity.urn}` }));
      (applyGlossaryFilters as jest.Mock).mockImplementation((results: any[]) => results);
      mockRequest.query = { start: 0, count: 1 };
      mockRequest.body = { customProperties: 'not-an-array' };
      await glossarySearchController(mockRequest, mockReply);
      expect(mockReply.send).toHaveBeenCalledWith({
        start: 0,
        count: 1,
        total: 1,
        results: [
          { urn: 'urn:li:glossaryTerm:1', name: 'Term urn:li:glossaryTerm:1' }
        ]
      });
    });

    it('should handle when customProperties is not present (default path)', async () => {
      (searchGlossaryTerms as jest.Mock).mockResolvedValue({
        data: { search: { searchResults: [
          { entity: { urn: 'urn:li:glossaryTerm:1' } }
        ], start: 0, count: 1, total: 1 } }
      });
      (glossaryTermToResponse as jest.Mock).mockImplementation(entity => ({ urn: entity.urn, name: `Term ${entity.urn}` }));
      (applyGlossaryFilters as jest.Mock).mockImplementation((results: any[]) => results);
      mockRequest.query = { start: 0, count: 1 };
      mockRequest.body = {};
      await glossarySearchController(mockRequest, mockReply);
      expect(mockReply.send).toHaveBeenCalledWith({
        start: 0,
        count: 1,
        total: 1,
        results: [
          { urn: 'urn:li:glossaryTerm:1', name: 'Term urn:li:glossaryTerm:1' }
        ]
      });
    });

    it('should handle errors thrown after filtering (catch block)', async () => {
      (searchGlossaryTerms as jest.Mock).mockResolvedValue({
        data: { search: { searchResults: [
          { entity: { urn: 'urn:li:glossaryTerm:1' } }
        ], start: 0, count: 1, total: 1 } }
      });
      (glossaryTermToResponse as jest.Mock).mockImplementation(entity => { throw new Error('transform fail'); });
      mockRequest.query = { start: 0, count: 1 };
      mockRequest.body = {};
      await glossarySearchController(mockRequest, mockReply);
      expect(mockReply.status).toHaveBeenCalledWith(500);
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'transform fail', details: undefined });
    });

    it('should handle when customProperties is null (no special pagination)', async () => {
      (searchGlossaryTerms as jest.Mock).mockResolvedValue({
        data: { search: { searchResults: [
          { entity: { urn: 'urn:li:glossaryTerm:1' } }
        ], start: 0, count: 1, total: 1 } }
      });
      (glossaryTermToResponse as jest.Mock).mockImplementation(entity => ({ urn: entity.urn, name: `Term ${entity.urn}` }));
      (applyGlossaryFilters as jest.Mock).mockImplementation((results: any[]) => results);
      mockRequest.query = { start: 0, count: 1 };
      mockRequest.body = { customProperties: null };
      await glossarySearchController(mockRequest, mockReply);
      expect(mockReply.send).toHaveBeenCalledWith({
        start: 0,
        count: 1,
        total: 1,
        results: [
          { urn: 'urn:li:glossaryTerm:1', name: 'Term urn:li:glossaryTerm:1' }
        ]
      });
    });

    it('should handle when customProperties is undefined (no special pagination)', async () => {
      (searchGlossaryTerms as jest.Mock).mockResolvedValue({
        data: { search: { searchResults: [
          { entity: { urn: 'urn:li:glossaryTerm:1' } }
        ], start: 0, count: 1, total: 1 } }
      });
      (glossaryTermToResponse as jest.Mock).mockImplementation(entity => ({ urn: entity.urn, name: `Term ${entity.urn}` }));
      (applyGlossaryFilters as jest.Mock).mockImplementation((results: any[]) => results);
      mockRequest.query = { start: 0, count: 1 };
      mockRequest.body = { customProperties: undefined };
      await glossarySearchController(mockRequest, mockReply);
      expect(mockReply.send).toHaveBeenCalledWith({
        start: 0,
        count: 1,
        total: 1,
        results: [
          { urn: 'urn:li:glossaryTerm:1', name: 'Term urn:li:glossaryTerm:1' }
        ]
      });
    });

    it('should use default start and count if not provided in query', async () => {
      (searchGlossaryTerms as jest.Mock).mockResolvedValue({
        data: { search: { searchResults: [
          { entity: { urn: 'urn:li:glossaryTerm:1' } },
          { entity: { urn: 'urn:li:glossaryTerm:2' } }
        ], start: 0, count: 2, total: 2 } }
      });
      (glossaryTermToResponse as jest.Mock).mockImplementation(entity => ({ urn: entity.urn, name: `Term ${entity.urn}` }));
      (applyGlossaryFilters as jest.Mock).mockImplementation((results: any[]) => results);
      mockRequest.query = {};
      mockRequest.body = {};
      await glossarySearchController(mockRequest, mockReply);
      expect(mockReply.send).toHaveBeenCalledWith({
        start: 0,
        count: 2,
        total: 2,
        results: [
          { urn: 'urn:li:glossaryTerm:1', name: 'Term urn:li:glossaryTerm:1' },
          { urn: 'urn:li:glossaryTerm:2', name: 'Term urn:li:glossaryTerm:2' }
        ]
      });
    });
  });

  describe('getGlossaryTermByUrnController', () => {
    it('should return a glossary term by URN', async () => {
      (getGlossaryTermByUrn as jest.Mock).mockResolvedValue({ urn: 'urn:li:glossaryTerm:1', name: 'Test Term' });
      (glossaryTermToResponse as jest.Mock).mockReturnValue({ urn: 'urn:li:glossaryTerm:1', name: 'Test Term' });
      mockRequest.query = { urn: 'urn:li:glossaryTerm:1' };
      await getGlossaryTermByUrnController(mockRequest, mockReply);
      expect(getGlossaryTermByUrn).toHaveBeenCalledWith(mockRequest, 'urn:li:glossaryTerm:1');
      expect(glossaryTermToResponse).toHaveBeenCalled();
      expect(mockReply.send).toHaveBeenCalledWith({ urn: 'urn:li:glossaryTerm:1', name: 'Test Term' });
    });

    it('should return 404 if glossary term not found', async () => {
      (getGlossaryTermByUrn as jest.Mock).mockResolvedValue(undefined);
      mockRequest.query = { urn: 'urn:li:glossaryTerm:404' };
      await getGlossaryTermByUrnController(mockRequest, mockReply);
      expect(mockReply.status).toHaveBeenCalledWith(404);
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Glossary term not found' });
    });

    it('should handle errors and return 500', async () => {
      (getGlossaryTermByUrn as jest.Mock).mockRejectedValue(new Error('fail'));
      mockRequest.query = { urn: 'urn:li:glossaryTerm:1' };
      await getGlossaryTermByUrnController(mockRequest, mockReply);
      expect(mockReply.status).toHaveBeenCalledWith(500);
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'fail', details: undefined });
    });
  });
}); 