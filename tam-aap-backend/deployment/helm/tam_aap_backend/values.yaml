replicaCount: 1

annotations:
  rollme: '{{ now | date "20060102150405" }}'

image:
  repository: tam-aap-backend
  tag: latest
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  port: 3020

ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: tam-aap-backend.127.0.0.1.nip.io
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []

livenessProbe:
  enabled: true
  path: /health
  port: 3020

readinessProbe:
  enabled: true
  path: /health
  port: 3020

resources: {}

env:
  CORS_ALLOWED_ORIGINS: >
    http://localhost:3000
  INTEGRATION_HUB_HOST: tam-aap-integrationhub-tam-aap-integrationhub
  INTEGRATION_HUB_PORT: 3010
  TAM_PORTAL_HOST: 0.0.0.0
  TAM_PORTAL_PORT: 3020

imagePullSecrets:

# Add additional environment variables as needed 