import { FastifyRequest } from 'fastify';

/**
 * Generic interface for GraphQL responses from DataHub
 */
export interface GraphQLResponse<T> {
  data: T;
  errors?: Array<{ message: string }>;
}

/**
 * DataHub client interface for making GraphQL queries
 */
export interface DataHubClient {
  query: <T>(query: string) => Promise<GraphQLResponse<T>>;
}

export interface DataHubError extends Error {
  details?: string[];
}

export interface DatahubClientOptions {
  endpoint?: string;
  token?: string;
  timeoutMs?: number;
}

declare module 'fastify' {
  interface FastifyRequest {
    datahub: DataHubClient;
  }
}