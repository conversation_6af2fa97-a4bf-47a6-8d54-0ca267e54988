import { Static } from '@sinclair/typebox';
import { GlossaryTerm, CustomProperty } from '../types/glossaryTypes.js';

// Individual filter functions
export function filterByInitialLetter(results: Static<typeof GlossaryTerm>[], initialLetter: string): Static<typeof GlossaryTerm>[] {
  if (!initialLetter || typeof initialLetter !== 'string') return results;
  const initial = initialLetter[0].toLowerCase();
  return results.filter(r => r.name[0]?.toLowerCase() === initial);
}

export function filterByExactSearchMatch(results: Static<typeof GlossaryTerm>[], value: boolean, filters: Record<string, any>): Static<typeof GlossaryTerm>[] {
  if (!value || !filters.search || typeof filters.search !== 'string') return results;
  const searchLower = filters.search.trim().toLowerCase();
  return results.filter(r =>
    (typeof r.name === 'string' && r.name.trim().toLowerCase().includes(searchLower)) ||
    (typeof r.description === 'string' && r.description.trim().toLowerCase().includes(searchLower))
  );
}

export function filterByCustomProperties(results: Static<typeof GlossaryTerm>[], customProperties: Static<typeof CustomProperty>[]): Static<typeof GlossaryTerm>[] {
  if (!Array.isArray(customProperties) || customProperties.length === 0) return results;
  return results.filter(r =>
    customProperties.every(cp =>
      Array.isArray(r.customProperties) && r.customProperties.some((prop: any) => prop.key === cp.key && prop.value === cp.value)
    )
  );
}

// Registry of available glossary filters
export const glossaryFilterRegistry: Record<string, (results: Static<typeof GlossaryTerm>[], value: any, filters: Record<string, any>) => Static<typeof GlossaryTerm>[]> = {
  initialLetter: (results, value) => filterByInitialLetter(results, value),
  exactSearchMatch: (results, value, filters) => filterByExactSearchMatch(results, value, filters),
  customProperties: (results, value) => filterByCustomProperties(results, value),
  // Add more filters here as needed
};

/**
 * Applies all registered filters to the results based on the filters object.
 * @param results Array of glossary search results
 * @param filters Object containing filter values
 * @returns Filtered array
 */
export function applyGlossaryFilters(
  results: Static<typeof GlossaryTerm>[],
  filters: Record<string, any>
): Static<typeof GlossaryTerm>[] {
  let filtered = results;

  // Always filter by substring match if search is present
  if (filters.search && typeof filters.search === 'string') {
    const searchLower = filters.search.trim().toLowerCase();
    filtered = filtered.filter(r =>
      (r.name?.trim?.().toLowerCase?.() || '').includes(searchLower) ||
      ((r.description?.trim?.().toLowerCase?.() || '').includes(searchLower))
    );
  }

  // Filter by initial letter
  if (filters.initialLetter && typeof filters.initialLetter === 'string' && filters.initialLetter.length > 0) {
    const initial = filters.initialLetter[0].toLowerCase();
    filtered = filtered.filter(r => (r.name?.[0]?.toLowerCase?.() || '') === initial);
  }

  // Filter by exact match (now using include matching in name or description)
  if (filters.exactSearchMatch && filters.search && typeof filters.search === 'string') {
    const searchLower = filters.search.trim().toLowerCase();
    filtered = filtered.filter(r =>
      (typeof r.name === 'string' && r.name.trim().toLowerCase().includes(searchLower)) ||
      (typeof r.description === 'string' && r.description.trim().toLowerCase().includes(searchLower))
    );
  }

  // Filter by customProperties if present (all must match)
  if (filters.customProperties && Array.isArray(filters.customProperties) && filters.customProperties.length > 0) {
    filtered = filtered.filter(r =>
      (filters.customProperties as { key: string; value: string }[]).every(cp =>
        Array.isArray(r.customProperties) && r.customProperties.some((prop: any) => prop.key === cp.key && prop.value === cp.value)
      )
    );
  }

  // Sort alphabetically by name (case-insensitive)
  filtered = filtered.sort((a, b) => (a.name || '').localeCompare(b.name || '', undefined, { sensitivity: 'base' }));

  return filtered;
}