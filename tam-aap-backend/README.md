# TAM AAP Backend

This project provides a Node.js Fastify service for integrating with external glossary and domain endpoints, acting as a proxy and transformation layer.

---

## API Documentation (Swagger UI)

Once the app is running, you can explore and test the available API endpoints using Swagger UI:

- Navigate to [http://localhost:3020/docs](http://localhost:3020/docs) in your browser.

This interactive documentation allows you to view all available endpoints, their parameters, and try them out directly.

---

## Local Development Setup

### Prerequisites
- Node.js (v18+ recommended)
- npm
- (Optional) Integration Hub or other backend service running (locally or remotely)

### 1. Install dependencies
```sh
npm ci
```

### 2. Configure environment variables
Create a `.env` file or set the following variables:

```
TAM_PORTAL_PORT=3020
TAM_PORTAL_HOST=0.0.0.0
INTEGRATION_HUB_PORT=3010
INTEGRATION_HUB_HOST=localhost  # or your integration hub host
```

### 3. Start the app
```sh
npm start
```

The app will be available at [http://localhost:3020](http://localhost:3020).

---

## Docker Deployment

### 1. Build and run with Docker Compose

Ensure you have a running integration hub or backend service (locally or remotely).

```sh
docker compose up --build
```

This will build and start the `tam-aap-backend` service, connecting to the integration hub at the configured host/port.

#### Environment variables (see `docker-compose.yml`):
- `INTEGRATION_HUB_HOST` should be set to `host.docker.internal` for host-based services, or the appropriate hostname/IP for remote services.

---

## Kubernetes/Helm Deployment (with kind and Ingress)

You can also follow these manual steps if you want more control:

### Prerequisites
- [kind](https://kind.sigs.k8s.io/) installed
- Docker Desktop running
- Helm installed
- **Integration Hub deployed in your cluster** (see its own deployment instructions)

For Cluster and Integration Hub setup, please refer to the `README.md` of the integration hub repository.

### 3. Build and load your Docker image into kind
```sh
docker build -t tam-aap-backend:latest .
kind load docker-image tam-aap-backend:latest --name kind
```

### 4. Deploy with Helm
```sh
kubectl create namespace integrationhub || true
helm upgrade --install tam-aap-backend ./deployment/helm/tam_aap_backend \
  --namespace integrationhub \
  -f deployment/helm/tam_aap_backend/values.yaml
```

### 5. Access the app via Ingress (nip.io wildcard DNS)
Open in your browser:
- http://tam-aap-backend.127.0.0.1.nip.io/           (welcome message or API root)
- http://tam-aap-backend.127.0.0.1.nip.io/health     (health check)
- http://tam-aap-backend.127.0.0.1.nip.io/docs       (Swagger UI)

No /etc/hosts changes are needed!

### 6. Troubleshooting
- Check pod status: `kubectl get pods -n integrationhub`
- Check logs: `kubectl logs -n integrationhub <pod-name>`
- Check ingress: `kubectl get ingress -n integrationhub`

---

## Troubleshooting
- Ensure the integration hub or backend service is reachable from your app container (network/firewall).
- For Docker, use `host.docker.internal` to access host services from the container.
- Check logs for errors: `docker compose logs -f` or `kubectl logs`.

---

# Plugins Directory

Place Fastify plugins (e.g., CORS, authentication, etc.) here. Each file should export a function compatible with Fastify's plugin system. 

---

## Postman Collections

The Postman collections for this project are available in Postman. You can run these collections to test the integration with DataHub once you have set up your DataHub instance. Ensure that your DataHub is running and properly configured to interact with the Postman collections.
