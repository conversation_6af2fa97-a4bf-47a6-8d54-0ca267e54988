# TAM AAP IntegrationHub

This project provides a Node.js proxy/service for integrating with DataHub's GraphQL API, including glossary and domain management endpoints.

---

## API Documentation (Swagger UI)

Once the app is running, you can explore and test the available API endpoints using Swagger UI:

- Navigate to [http://localhost:3010/docs](http://localhost:3010/docs) in your browser.

This interactive documentation allows you to view all available endpoints, their parameters, and try them out directly.

---

## Local Development Setup

### Prerequisites
- Node.js (v18+ recommended)
- npm
- DataHub GMS instance running (locally or remotely)

### 1. Install dependencies
```sh
npm ci
```

### 2. Configure environment variables
Create a `.env` file or set the following variables:

```
INTEGRATION_HUB_PORT=3010
INTEGRATION_HUB_HOST=0.0.0.0
DATAHUB_PORT=8080
DATAHUB_HOST=localhost  # or your DataHub GMS host
```

### 3. Start the app
```sh
npm start
```

The app will be available at [http://localhost:3010](http://localhost:3010).

---

## Docker Deployment

### 1. Build and run with Docker Compose

Ensure you have a running DataHub GMS instance (locally or remotely).

#### Example: Using DataHub GMS on your host

```sh
docker-compose up --build
```

This will build and start the `datahub-integration` service, connecting to DataHub GMS at `host.docker.internal:8080`.

#### Environment variables (see `docker-compose.yml`):
- `DATAHUB_HOST` should be set to `host.docker.internal` for host-based GMS, or the appropriate hostname/IP for remote GMS.

---

## Quickstart: One-command Cluster & Deployment

You can set up your kind cluster, ingress, Docker image, and deploy the Helm chart with a single script:

```sh
chmod +x local_tests/setup-kind-integrationhub.sh
./local_tests/setup-kind-integrationhub.sh
```

This will:
- Create (or recreate) your kind cluster with Ingress support
- Build and load your Docker image
- Create the `integrationhub` namespace
- Install/upgrade the Helm chart
- Print the URL for your app (using nip.io, no /etc/hosts needed)

---

## Kubernetes/Helm Deployment (with kind and Ingress)

You can also follow these manual steps if you want more control:

### Prerequisites
- [kind](https://kind.sigs.k8s.io/) installed
- Docker Desktop running
- Helm installed

### 1. Create kind cluster with Ingress support
Use this kind config (example: `local_tests/kind-config.yaml`):

```yaml
kind: Cluster
apiVersion: kind.x-k8s.io/v1alpha4
nodes:
  - role: control-plane
    kubeadmConfigPatches:
      - |
        kind: InitConfiguration
        nodeRegistration:
          kubeletExtraArgs:
            node-labels: "ingress-ready=true"
    extraPortMappings:
      - containerPort: 80
        hostPort: 80
        protocol: TCP
      - containerPort: 443
        hostPort: 443
        protocol: TCP
```

```sh
kind create cluster --config local_tests/kind-config.yaml
```

### 2. Install NGINX Ingress controller
```sh
kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.10.1/deploy/static/provider/kind/deploy.yaml
```

### 3. Build and load your Docker image into kind
```sh
docker build -t tam-aap-integrationhub:latest .
kind load docker-image tam-aap-integrationhub:latest --name kind
```

### 4. Deploy with Helm
```sh
kubectl create namespace integrationhub || true
helm upgrade --install tam-aap-integrationhub ./deployment/helm/tam_aap_integrationhub \
  --namespace integrationhub \
  -f deployment/helm/tam_aap_integrationhub/values-dev.yaml
```

### 5. Access the app via Ingress (nip.io wildcard DNS)
Open in your browser:
- http://integrationhub.127.0.0.1.nip.io/           (welcome message)
- http://integrationhub.127.0.0.1.nip.io/health     (health check)

No /etc/hosts changes are needed!

### 6. Troubleshooting
- Check pod status: `kubectl get pods -n integrationhub`
- Check logs: `kubectl logs -n integrationhub <pod-name>`
- Check ingress: `kubectl get ingress -n integrationhub`

---

## DataHub CLI Setup

For working with DataHub's glossary and metadata ingestion, you'll need the DataHub CLI tool. We provide a convenient setup script that creates a Python virtual environment and installs the DataHub CLI:

### Prerequisites
- Python 3.11

### Setup Steps

1. Run the setup script:
```sh
chmod +x local_tests/setup-python-env-datahub.sh
./local_tests/setup-python-env-datahub.sh
```

This script will:
- Create a Python 3.11 virtual environment (`.venv`)
- Activate the virtual environment
- Upgrade pip, setuptools, and wheel
- Install the DataHub CLI with development dependencies (if possible)
- Fall back to the base DataHub CLI package if dev dependencies fail

### Using the Environment

After setup, you can:

1. Activate the environment in a new shell:
```sh
source .venv/bin/activate
```

2. Verify the installation:
```sh
datahub --help
```

3. Use DataHub CLI commands for tasks like:
   - Ingesting glossary terms
   - Managing metadata
   - Querying DataHub's API

Note: Make sure your DataHub instance is running before using the CLI.

---

## Postman Collections

The Postman collections for this project are available in Postman. You can run these collections to test the integration with DataHub once you have set up your DataHub instance. Ensure that your DataHub is running and properly configured to interact with the Postman collections.

---

## Running Unit Tests

To ensure the quality and functionality of the code, you can run the unit tests using the following command:

```sh
npm run test
```

This command will execute all the unit tests defined in the project, providing feedback on the code's correctness and reliability.

