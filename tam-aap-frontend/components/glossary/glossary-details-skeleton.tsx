import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs";

export default function GlossaryDetailsSkeleton() {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="container mx-auto py-6">
        <div className="flex items-center gap-2 mb-6">
          <Skeleton className="h-4 w-20" />
          <span>/</span>
          <Skeleton className="h-4 w-32" />
        </div>

        <div className="bg-card card-foreground border-border rounded-lg overflow-hidden mb-6 p-6">
          <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
            <Skeleton className="h-10 w-64" />
            <Skeleton className="h-4 w-32" />
          </div>
          <Skeleton className="h-6 w-full mb-2" />
          <Skeleton className="h-6 w-3/4" />
        </div>

        <Tabs defaultValue="details" className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-0">
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="discussion">Discussion</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="mt-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="p-4 border border-border rounded-lg">
                  <Skeleton className="h-4 w-24 mb-2" />
                  <Skeleton className="h-6 w-40" />
                </div>
              ))}
            </div>

            <Skeleton className="h-6 w-64 mb-6" />

            <Skeleton className="h-6 w-72 mb-4" />
            <div className="flex flex-wrap gap-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <div
                  key={i}
                  className="py-9 w-60 border border-border rounded-lg"
                >
                  <div className="px-6">
                    <Skeleton className="h-8 w-8 mb-4" />
                    <Skeleton className="h-6 w-32" />
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="discussion" className="mt-6">
            <div className="p-6 border border-border rounded-lg">
              <Skeleton className="h-4 w-48 mb-4" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-3/4" />
            </div>
          </TabsContent>

          <TabsContent value="history" className="mt-6">
            <div className="p-6 border border-border rounded-lg">
              <Skeleton className="h-4 w-48 mb-4" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-3/4" />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
