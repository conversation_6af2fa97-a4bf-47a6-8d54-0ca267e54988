import fastify from 'fastify';
import fp from 'fastify-plugin';
import fetch from 'node-fetch';
import datahubClient from '../datahub-client';
import { DATAHUB_GRAPHQL_ENDPOINT, DATAHUB_TOKEN } from '../../configs/config';

jest.mock('node-fetch', () => jest.fn());
const { Response } = jest.requireActual('node-fetch');

describe('datahubClient plugin', () => {
  const endpoint = 'http://mock-endpoint/graphql';
  const token = 'mock-token';
  const query = '{ testQuery }';
  const mockResponse = { data: { test: 'ok' } };

  let app: ReturnType<typeof fastify>;

  beforeEach(() => {
    ((fetch as unknown) as jest.Mock).mockReset();
    app = fastify();
  });

  it('decorates request with datahub client', async () => {
    await app.register(fp(datahubClient), { endpoint, token });
    app.get('/test', async (request: any, reply: any) => {
      expect(request.datahub).toBeDefined();
      return reply.send({ ok: true });
    });
    await app.ready();
    const res = await app.inject({ method: 'GET', url: '/test' });
    expect(res.statusCode).toBe(200);
  });

  it('decorates request with datahub client (getter coverage)', async () => {
    await app.register(fp(datahubClient), { endpoint, token });
    app.get('/getter-test', async (request: any, reply: any) => {
      // Just access the getter
      const client = request.datahub;
      expect(client).toBeDefined();
      return reply.send({ ok: true });
    });
    await app.ready();
    const res = await app.inject({ method: 'GET', url: '/getter-test' });
    expect(res.statusCode).toBe(200);
  });

  it('calls fetch with correct arguments and returns data', async () => {
    ((fetch as unknown) as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => mockResponse,
    });
    await app.register(fp(datahubClient), { endpoint, token });
    app.post('/test', async (request: any, reply: any) => {
      const result = await request.datahub.query(query);
      return reply.send(result);
    });
    await app.ready();
    const req = { method: 'POST', url: '/test', payload: {} };
    const res = await app.inject(req);
    expect(fetch).toHaveBeenCalledWith(
      endpoint,
      expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({ 'Content-Type': 'application/json', Authorization: `Bearer ${token}` }),
        body: JSON.stringify({ query, variables: {} }),
      })
    );
    expect(res.json()).toEqual(mockResponse);
  });

  it('logs and throws when fetch returns non-ok response', async () => {
    ((fetch as unknown) as jest.Mock).mockResolvedValue({
      ok: false,
      status: 500,
      statusText: 'Internal Server Error',
      text: async () => 'fail',
    });
    await app.register(fp(datahubClient), { endpoint, token });
    app.post('/test', async (request: any, reply: any) => {
      await expect(request.datahub.query(query)).rejects.toThrow('GraphQL request failed: 500 Internal Server Error');
      return reply.send();
    });
    await app.ready();
    const req = { method: 'POST', url: '/test', payload: {} };
    await app.inject(req);
  });

  it('logs and throws when fetch throws (network error)', async () => {
    ((fetch as unknown) as jest.Mock).mockRejectedValue(new Error('network error'));
    await app.register(fp(datahubClient), { endpoint, token });
    app.post('/test', async (request: any, reply: any) => {
      await expect(request.datahub.query(query)).rejects.toThrow('network error');
      return reply.send();
    });
    await app.ready();
    const req = { method: 'POST', url: '/test', payload: {} };
    await app.inject(req);
  });

  it('uses fallback config values for endpoint and token if not provided in options', async () => {
    ((fetch as unknown) as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => mockResponse,
    });
    await app.register(fp(datahubClient), {}); // No endpoint or token
    app.post('/test', async (request: any, reply: any) => {
      await request.datahub.query(query);
      return reply.send({ ok: true });
    });
    await app.ready();
    const req = { method: 'POST', url: '/test', payload: {} };
    await app.inject(req);
    expect(fetch).toHaveBeenCalledWith(
      DATAHUB_GRAPHQL_ENDPOINT,
      expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({ 'Content-Type': 'application/json' }),
        body: JSON.stringify({ query, variables: {} }),
      })
    );
  });

  it('aborts the request on timeout', async () => {
    // Mock fetch to reject when the signal is aborted
    ((fetch as unknown) as jest.Mock).mockImplementation((_url, opts) => {
      return new Promise((_resolve, reject) => {
        opts.signal.addEventListener('abort', () => {
          reject(new Error('Aborted'));
        });
      });
    });
    await app.register(fp(datahubClient), { endpoint, token, timeoutMs: 10 }); // Short timeout
    app.post('/timeout-test', async (request: any, reply: any) => {
      await expect(request.datahub.query(query)).rejects.toThrow('Aborted');
      return reply.send();
    });
    await app.ready();
    const req = { method: 'POST', url: '/timeout-test', payload: {} };
    await app.inject(req);
  });
}); 