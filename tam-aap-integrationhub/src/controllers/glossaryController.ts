import { FastifyRequest, FastifyReply } from 'fastify';
import { Static } from '@sinclair/typebox';
import { GlossarySearchQuery, PaginationQuery } from '../types/glossaryTypes.js';
import { searchGlossaryTerms, getGlossaryTermByUrn } from '../services/glossaryService.js';
import { glossaryTermToResponse } from '../transformers/glossaryTransformer.js';
import { applyGlossaryFilters } from '../utils/glossaryFilters.js';

type SearchRequest = FastifyRequest<{
  Querystring: Static<typeof GlossarySearchQuery> & Static<typeof PaginationQuery>;
  Body: { customProperties?: { key: string; value: string }[] };
}>;

export async function glossarySearchController(request: SearchRequest, reply: FastifyReply) {
  try {
    // Merge query and customProperties from body for filters
    const filters: Record<string, any> = { ...request.query };
    if (request.body && request.body.customProperties) {
      filters.customProperties = request.body.customProperties;
    }

    // If customProperties is present, fetch all results for correct filtering
    let serviceFilters = { ...filters };
    if (filters.customProperties && Array.isArray(filters.customProperties) && filters.customProperties.length > 0) {
      serviceFilters.count = undefined;
    }
    const response = await searchGlossaryTerms(request, serviceFilters);
    const search = response.data.search;

    // Use transformer for results
    let filteredResults = applyGlossaryFilters(
      (search.searchResults || []).map((result: { entity: any }) => glossaryTermToResponse(result.entity)),
      filters
    );

    // Paginate after filtering if customProperties was used
    let paginatedResults = filteredResults;
    let start = typeof filters.start === 'number' ? filters.start : 0;
    let count = typeof filters.count === 'number' ? filters.count : filteredResults.length;
    if (filters.customProperties && Array.isArray(filters.customProperties) && filters.customProperties.length > 0) {
      paginatedResults = filteredResults.slice(start, start + count);
    }

    return reply.send({
      start,
      count: paginatedResults.length,
      total: filteredResults.length,
      results: paginatedResults
    });
  } catch (error: any) {
    request.log.error('Failed to fetch glossary terms:', error);
    return reply.status(500).send({
      error: error.message || 'Failed to fetch glossary terms',
      details: error.details || undefined,
    });
  }
}

export async function getGlossaryTermByUrnController(request: FastifyRequest<{ Querystring: { urn: string } }>, reply: FastifyReply) {
  const { urn } = request.query;
  try {
    const term = await getGlossaryTermByUrn(request, urn);
    if (!term) {
      return reply.status(404).send({ error: 'Glossary term not found' });
    }
    return reply.send(glossaryTermToResponse(term));
  } catch (error: any) {
    request.log.error('Failed to fetch glossary term by URN:', error);
    return reply.status(500).send({
      error: error.message || 'Failed to fetch glossary term',
      details: error.details || undefined,
    });
  }
} 