import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Search } from "lucide-react";
import { debounce } from "lodash";
import { useCallback, ReactNode } from "react";
import Image from "next/image";

interface HeroSectionProps {
  title: string;
  description: string;
  backgroundImage?: string;
  searchTerm?: string;
  onSearchChange?: (value: string) => void;
  buttonText?: string;
  buttonLink?: string;
  buttonIcon?: ReactNode;
  searchPlaceholder?: string;
}

export default function HeroSection({
  title,
  description,
  backgroundImage = "/slides/slide2.jpg",
  searchTerm = "",
  onSearchChange,
  buttonText = "Learn about our committees",
  buttonLink = "#",
  buttonIcon = <span className="ml-2">→</span>,
  searchPlaceholder = "Search",
}: HeroSectionProps) {
  // TODO: Use debouce hook here
  const debouncedSearch = useCallback(
    debounce((term: string) => {
      if (onSearchChange) {
        onSearchChange(term);
      }
    }, 300),
    [onSearchChange] // eslint-disable-line react-hooks/exhaustive-deps
  );

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (debouncedSearch) {
      const value = e.target.value;
      debouncedSearch(value);
    }
  };

  return (
    <div className="relative bg-background text-foreground py-26">
      <div className="absolute inset-0 overflow-hidden z-0">
        <div className="relative w-full h-full">
          <Image
            src={backgroundImage}
            alt="Background image"
            fill
            sizes="100vw"
            priority
            className="object-cover"
            style={{ objectPosition: "center" }}
          />
        </div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="flex flex-col gap-2 items-start">
          <h1 className="text-[42px] leading-[1.2] tracking-tight font-bold">
            {title}
          </h1>
          <p className="text-xl max-w-4xl leading-[1.3] text-slate-300 mb-6">
            {description}
          </p>

          <div className="flex justify-between w-full gap-4">
            {onSearchChange && (
              <div className="relative w-full max-w-md">
                <div className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-slate-400 z-10">
                  <Search size={20} />
                </div>
                <Input
                  placeholder={searchPlaceholder}
                  className="pl-10 h-12 bg-white/10 backdrop-blur-sm border-white/20 text-white placeholder:text-slate-400 w-full"
                  defaultValue={searchTerm}
                  onChange={handleSearchChange}
                />
              </div>
            )}

            <Button
              variant="primary"
              className="h-12 p-4 rounded-lg bg-[#2E55A5] bg-[linear-gradient(316deg,rgba(0,0,0,0.3)-12.29%,rgba(255,255,255,0.15)112.77%)] bg-blend-soft-light *:inline-flex justify-center items-center gap-2"
              onClick={() => (window.location.href = buttonLink)}
            >
              {buttonText} {buttonIcon}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
