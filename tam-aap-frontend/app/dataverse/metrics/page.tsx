"use client";

import { useState, useEffect } from "react";
import { fetchMetricsItems } from "@/lib/api";
import { MetricsItem, MetricsItemsQueryParams } from "@/types/dataverse";
import HeroSection from "@/components/dataverse/hero-section";
import DepartmentSidebar from "@/components/dataverse/department-sidebar";
import MetricsCardList from "@/components/metrics/metrics-card-list";
import MetricsFilter, {
  FilterOption,
} from "@/components/metrics/metrics-filter";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbSeparator,
  BreadcrumbPage,
  BreadcrumbList,
} from "@/components/ui/breadcrumb";

const ALL = "All";

export default function MetricsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [metricsItems, setMetricsItems] = useState<MetricsItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeDomain, setActiveDomain] = useState<string | null>(ALL);
  const [sidebarExpanded, setSidebarExpanded] = useState(true);
  const [owningEntitiesOptions, setOwningEntitiesOptions] = useState<
    FilterOption[]
  >([]);
  const [endorsedByOptions, setEndorsedByOptions] = useState<FilterOption[]>(
    []
  );
  const [activeFilters, setActiveFilters] = useState<{
    owningEntity: string | null;
    endorsedBy: string | null;
  }>({
    owningEntity: null,
    endorsedBy: null,
  });

  // Only run this effect when dependencies actually change
  useEffect(() => {
    const fetchData = async () => {
      await loadMetricsItems();
    };

    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    searchTerm,
    activeDomain,
    activeFilters.owningEntity,
    activeFilters.endorsedBy,
  ]);

  // Extract unique filter options from loaded data - only run when metricsItems change
  useEffect(() => {
    if (metricsItems.length > 0) {
      // Extract unique owning entities
      const entities = Array.from(
        new Set(
          metricsItems
            .map((item) => item.owningEntity)
            .filter(Boolean) as string[]
        )
      ).map((entity) => ({
        id: entity,
        label: entity,
      }));

      // Extract unique endorsed by values
      const endorsers = Array.from(
        new Set(
          metricsItems
            .map((item) => item.endorsedBy)
            .flat()
            .filter(Boolean) as string[]
        )
      ).map((endorser) => ({
        id: endorser,
        label: endorser,
      }));

      setOwningEntitiesOptions(entities);
      setEndorsedByOptions(endorsers);
    }
  }, [metricsItems]);

  const loadMetricsItems = async () => {
    setLoading(true);
    try {
      const params: MetricsItemsQueryParams = {};

      if (searchTerm) {
        params.search = searchTerm;
      }

      if (activeDomain && activeDomain !== ALL) {
        params.domain = activeDomain;
      }

      if (activeFilters.owningEntity) {
        params.owningEntity = activeFilters.owningEntity;
      }

      if (activeFilters.endorsedBy) {
        params.endorsedBy = activeFilters.endorsedBy;
      }

      const items = await fetchMetricsItems(params);
      setMetricsItems(items.results);
    } catch (error) {
      console.error("Failed to load metrics items:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
  };

  const handleDepartmentClick = (dept: string) => {
    setActiveDomain(activeDomain === dept ? ALL : dept);
  };

  const toggleSidebar = () => {
    setSidebarExpanded(!sidebarExpanded);
  };

  const handleFilterChange = (filters: {
    owningEntity: string | null;
    endorsedBy: string | null;
  }) => {
    setActiveFilters(filters);
  };

  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="container mx-auto px-6 py-2">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Metrics</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      <HeroSection
        title="Measure what matters"
        searchPlaceholder="Search Airport Metrics"
        description="Airport community recommended metrics and benchmarks to measure and compare performance across airports"
        onSearchChange={handleSearchChange}
        searchTerm={searchTerm}
      />

      <div className="container mx-auto px-6 mt-6">
        <div className="flex flex-col md:flex-row gap-6">
          <DepartmentSidebar
            expanded={sidebarExpanded}
            toggleSidebar={toggleSidebar}
            departments={[
              { name: "Airfield" },
              { name: "Baggage" },
              { name: "Commercial" },
              { name: "Facilities Management" },
              { name: "Finance" },
              { name: "Flight Operations" },
              { name: "Landside" },
              { name: "Maintenance" },
              { name: "Passenger" },
              { name: "Regulatory & Legal" },
              { name: "Safety & Compliance" },
              { name: "Security" },
              { name: "Sustainability" },
              { name: "Technology" },
              { name: "Terminal" },
            ]}
            activeDepartment={activeDomain}
            onDepartmentClick={handleDepartmentClick}
          />

          <div className="flex-1">
            {/* Add filter component above metrics list */}
            <MetricsFilter
              onFilterChange={handleFilterChange}
              owningEntities={owningEntitiesOptions}
              endorsedByOptions={endorsedByOptions}
            />

            <div className="mt-4">
              {loading ? (
                <div className="flex justify-center items-center h-64">
                  <div className="text-muted-foreground">
                    Loading metrics...
                  </div>
                </div>
              ) : metricsItems.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">No metrics found.</p>
                </div>
              ) : (
                <MetricsCardList items={metricsItems} />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
