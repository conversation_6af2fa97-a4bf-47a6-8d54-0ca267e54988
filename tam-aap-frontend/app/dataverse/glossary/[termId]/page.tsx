"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { fetchGlossaryItemById } from "@/lib/api";
import { GlossaryItem } from "@/types/dataverse";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { Card, CardContent } from "@/components/ui/card";
import GlossaryDetailsSkeleton from "@/components/glossary/glossary-details-skeleton";
import { formatDate } from "@/lib/utils";

export default function GlossaryDetailsPage() {
  const { termId } = useParams() as { termId: string };
  const [glossaryItem, setGlossaryItem] = useState<GlossaryItem | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const loadGlossaryItem = async () => {
      setLoading(true);
      try {
        const item = await fetchGlossaryItemById(termId);
        setGlossaryItem(item);
      } catch (error) {
        console.error("Failed to load glossary item:", error);
      } finally {
        setLoading(false);
      }
    };

    if (termId) {
      loadGlossaryItem();
    }
  }, [termId]);

  if (loading) {
    return <GlossaryDetailsSkeleton />;
  }

  if (!glossaryItem) {
    return (
      <div className="container mx-auto px-6 py-12">
        <div className="text-center">
          <h1 className="text-2xl font-semibold mb-4">
            Glossary Term Not Found
          </h1>
          <p className="text-muted-foreground mb-6">
            The glossary term you are looking for could not be found.
          </p>
          <Link
            href="/dataverse/glossary"
            className="text-primary hover:underline flex items-center justify-center gap-2"
          >
            <span>Return to Glossary</span>
            <ArrowRight className="h-4 w-4" />
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="container mx-auto px-6 py-2">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/dataverse/glossary">
                Glossary
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{glossaryItem.title}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <Card className="bg-card card-foreground border-border rounded-lg overflow-hidden my-6 p-4">
          <CardContent className="p-0">
            <div className="flex flex-col md:flex-row md:items-center justify-between">
              <h1 className="text-white text-3xl font-semibold leading-10">
                {glossaryItem.title}
              </h1>

              <div className="text-zinc-400 text-sm">
                Last Updated: {formatDate(glossaryItem.lastUpdated)}
              </div>
            </div>
            <p className="text-zinc-400 text-xl">{glossaryItem.description}</p>
          </CardContent>
        </Card>

        <Tabs defaultValue="details">
          <TabsList>
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="discussion">Discussion</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="mt-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 w-full">
              <Card className="bg-card border-border rounded-lg overflow-hidden p-4">
                <CardContent className="p-0">
                  <h3 className="text-zinc-400 mb-2">Acronym</h3>
                  <p className="text-white text-xl font-medium">
                    {glossaryItem.acronym || "N/A"}
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-card border-border rounded-lg overflow-hidden p-4">
                <CardContent className="p-0">
                  <h3 className="text-zinc-400 mb-2">Domain</h3>
                  <p className="text-white text-xl font-medium">
                    {glossaryItem.domains.join(", ")}
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-card border-border rounded-lg overflow-hidden p-4">
                <CardContent className="p-0">
                  <h3 className="text-zinc-400 mb-2">Synonyms</h3>
                  <p className="text-white text-xl font-medium">
                    {glossaryItem.synonyms?.length
                      ? glossaryItem.synonyms.join(", ")
                      : "N/A"}
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-card border-border rounded-lg overflow-hidden p-4">
                <CardContent className="p-0">
                  <h3 className="text-zinc-400 mb-2">
                    Definition Owning Entity
                  </h3>
                  <p className="text-white text-xl font-medium">
                    {glossaryItem.owningEntity || "N/A"}
                  </p>
                </CardContent>
              </Card>
            </div>

            <Link
              href="/dataverse/committees"
              className="text-blue-500 text-xl font-normal flex items-center gap-2 hover:underline justify-self-end mt-6"
            >
              Learn about our committees <ArrowRight className="h-4 w-4" />
            </Link>

            <div>
              <h3 className="text-white text-xl font-medium">
                Reviewed and approved by
              </h3>

              <div className="flex flex-wrap gap-4 mt-4">
                {glossaryItem.reviewedBy?.map((reviewer) => (
                  <Card className="py-9 w-60" key={reviewer.id}>
                    <CardContent className="px-6">
                      <div key={reviewer.id} className="relative w-8 h-8 mb-4">
                        <Image
                          src={`/svg/${reviewer.department}.svg`}
                          alt={reviewer.name}
                          fill
                          className="object-contain"
                        />
                      </div>

                      <p className="text-zinc-400 text-xl font-semibold break-words overflow-hidden">
                        {reviewer.name}
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="discussion" className="mt-6">
            <Card className="bg-card border-border rounded-lg overflow-hidden">
              <CardContent className="p-6">
                <div className="mb-4">
                  <span className="text-muted-foreground">
                    Last Updated: Feb 16, 2025
                  </span>
                </div>
                <p>
                  This page shows the history of discussion to this glossary
                  term over time. Currently, no discussion data is available for
                  this term.
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="mt-6">
            <Card className="bg-card border-border rounded-lg overflow-hidden">
              <CardContent className="p-6">
                <div className="mb-4">
                  <span className="text-muted-foreground">
                    Last Updated: Feb 16, 2025
                  </span>
                </div>
                <p>
                  This page shows the history of changes made to this glossary
                  term over time. Currently, no historical data is available for
                  this term.
                </p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
