import { domainSearchController, getDomainByUrnController } from '../domainController.js';
import { searchDomains, getDomainByUrn } from '../../services/domainService.js';
import { domainToResponse } from '../../transformers/domainTransformer.js';
import { applyDomainFilters } from '../../utils/domainFilters.js';

// Mock dependencies
jest.mock('../../services/domainService.js');
jest.mock('../../transformers/domainTransformer.js');
jest.mock('../../utils/domainFilters.js');

// Mock implementations
const mockSearchDomains = searchDomains as jest.MockedFunction<typeof searchDomains>;
const mockGetDomainByUrn = getDomainByUrn as jest.MockedFunction<typeof getDomainByUrn>;
const mockDomainToResponse = domainToResponse as jest.MockedFunction<typeof domainToResponse>;
const mockApplyDomainFilters = applyDomainFilters as jest.MockedFunction<typeof applyDomainFilters>;

describe('domainController', () => {
  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('domainSearchController', () => {
    it('should return search results successfully', async () => {
      // Mock data
      const mockRequest = {
        query: {
          search: 'test',
          initialLetter: 'T',
          start: 0,
          count: 10
        },
        log: {
          error: jest.fn()
        }
      } as any;

      const mockReply = {
        send: jest.fn().mockReturnThis(),
        status: jest.fn().mockReturnThis()
      } as any;

      const mockSearchResponse = {
        data: {
          search: {
            start: 0,
            count: 2,
            total: 2,
            searchResults: [
              {
                entity: {
                  urn: 'urn:li:domain:1',
                  properties: {
                    name: 'Test Domain 1',
                    description: 'Description 1'
                  }
                }
              },
              {
                entity: {
                  urn: 'urn:li:domain:2',
                  properties: {
                    name: 'Test Domain 2',
                    description: 'Description 2'
                  }
                }
              }
            ]
          }
        }
      };

      const mockTransformedDomains = [
        {
          urn: 'urn:li:domain:1',
          name: 'Test Domain 1',
          description: 'Description 1'
        },
        {
          urn: 'urn:li:domain:2',
          name: 'Test Domain 2',
          description: 'Description 2'
        }
      ];

      // Set up mocks
      mockSearchDomains.mockResolvedValue(mockSearchResponse);
      mockDomainToResponse
        .mockReturnValueOnce(mockTransformedDomains[0])
        .mockReturnValueOnce(mockTransformedDomains[1]);
      mockApplyDomainFilters.mockReturnValue(mockTransformedDomains);

      // Call the controller
      await domainSearchController(mockRequest, mockReply);

      // Assertions
      expect(mockSearchDomains).toHaveBeenCalledWith(mockRequest, mockRequest.query);
      expect(mockDomainToResponse).toHaveBeenCalledTimes(2);
      expect(mockApplyDomainFilters).toHaveBeenCalledWith(mockTransformedDomains, mockRequest.query);
      expect(mockReply.send).toHaveBeenCalledWith({
        start: 0,
        count: 2,
        total: 2,
        results: mockTransformedDomains
      });
    });

    it('should handle empty search results', async () => {
      // Mock data
      const mockRequest = {
        query: {
          search: 'nonexistent',
          start: 0,
          count: 10
        },
        log: {
          error: jest.fn()
        }
      } as any;

      const mockReply = {
        send: jest.fn().mockReturnThis(),
        status: jest.fn().mockReturnThis()
      } as any;

      const mockSearchResponse = {
        data: {
          search: {
            start: 0,
            count: 0,
            total: 0,
            searchResults: [] // Empty array instead of undefined
          }
        }
      };

      // Set up mocks
      mockSearchDomains.mockResolvedValue(mockSearchResponse);
      mockApplyDomainFilters.mockReturnValue([]);

      // Call the controller
      await domainSearchController(mockRequest, mockReply);

      // Assertions
      expect(mockSearchDomains).toHaveBeenCalledWith(mockRequest, mockRequest.query);
      expect(mockDomainToResponse).not.toHaveBeenCalled(); // Should not be called with empty results
      expect(mockApplyDomainFilters).toHaveBeenCalledWith([], mockRequest.query);
      expect(mockReply.send).toHaveBeenCalledWith({
        start: 0,
        count: 0,
        total: 0,
        results: []
      });
    });

    it('should handle null searchResults in response', async () => {
      // Mock data
      const mockRequest = {
        query: {
          search: 'nonexistent',
          start: 0,
          count: 10
        },
        log: {
          error: jest.fn()
        }
      } as any;

      const mockReply = {
        send: jest.fn().mockReturnThis(),
        status: jest.fn().mockReturnThis()
      } as any;

      // Create a response with null searchResults to test the || [] branch
      const mockSearchResponse = {
        data: {
          search: {
            start: 0,
            count: 0,
            total: 0,
            searchResults: null // Explicitly null to test the || [] branch
          }
        }
      };

      // Set up mocks
      mockSearchDomains.mockResolvedValue(mockSearchResponse as any);
      mockApplyDomainFilters.mockReturnValue([]);

      // Call the controller
      await domainSearchController(mockRequest, mockReply);

      // Assertions
      expect(mockSearchDomains).toHaveBeenCalledWith(mockRequest, mockRequest.query);
      // The controller should convert null to [] before mapping
      expect(mockApplyDomainFilters).toHaveBeenCalledWith([], mockRequest.query);
      expect(mockReply.send).toHaveBeenCalledWith({
        start: 0,
        count: 0,
        total: 0,
        results: []
      });
    });

    it('should handle errors properly', async () => {
      // Mock data
      const mockRequest = {
        query: {
          search: 'test'
        },
        log: {
          error: jest.fn()
        }
      } as any;

      const mockReply = {
        send: jest.fn().mockReturnThis(),
        status: jest.fn().mockReturnThis()
      } as any;

      const mockError = new Error('Test error');
      (mockError as any).details = ['Error detail 1', 'Error detail 2'];

      // Set up mocks
      mockSearchDomains.mockRejectedValue(mockError);

      // Call the controller
      await domainSearchController(mockRequest, mockReply);

      // Assertions
      expect(mockRequest.log.error).toHaveBeenCalled();
      expect(mockReply.status).toHaveBeenCalledWith(500);
      expect(mockReply.send).toHaveBeenCalledWith({
        error: 'Test error',
        details: ['Error detail 1', 'Error detail 2']
      });
    });

    it('should handle errors without details', async () => {
      // Mock data
      const mockRequest = {
        query: {
          search: 'test'
        },
        log: {
          error: jest.fn()
        }
      } as any;

      const mockReply = {
        send: jest.fn().mockReturnThis(),
        status: jest.fn().mockReturnThis()
      } as any;

      // Set up mocks
      mockSearchDomains.mockRejectedValue(new Error('Test error'));

      // Call the controller
      await domainSearchController(mockRequest, mockReply);

      // Assertions
      expect(mockReply.status).toHaveBeenCalledWith(500);
      expect(mockReply.send).toHaveBeenCalledWith({
        error: 'Test error',
        details: undefined
      });
    });

    it('should handle errors with empty message', async () => {
      // Mock data
      const mockRequest = {
        query: {
          search: 'test'
        },
        log: {
          error: jest.fn()
        }
      } as any;

      const mockReply = {
        send: jest.fn().mockReturnThis(),
        status: jest.fn().mockReturnThis()
      } as any;

      // Create an error with empty message
      const error = new Error('');

      // Set up mocks
      mockSearchDomains.mockRejectedValue(error);

      // Call the controller
      await domainSearchController(mockRequest, mockReply);

      // Assertions
      expect(mockReply.status).toHaveBeenCalledWith(500);
      expect(mockReply.send).toHaveBeenCalledWith({
        error: 'Failed to fetch domains', // Default message
        details: undefined
      });
    });
  });

  describe('getDomainByUrnController', () => {
    it('should return a domain by URN successfully', async () => {
      // Mock data
      const mockRequest = {
        query: {
          urn: 'urn:li:domain:1'
        },
        log: {
          error: jest.fn()
        }
      } as any;

      const mockReply = {
        send: jest.fn().mockReturnThis(),
        status: jest.fn().mockReturnThis()
      } as any;

      const mockDomain = {
        urn: 'urn:li:domain:1',
        properties: {
          name: 'Test Domain',
          description: 'Test Description'
        }
      };

      // Set up mocks
      mockGetDomainByUrn.mockResolvedValue(mockDomain);

      // Call the controller
      await getDomainByUrnController(mockRequest, mockReply);

      // Assertions
      expect(mockGetDomainByUrn).toHaveBeenCalledWith(mockRequest, 'urn:li:domain:1');
      expect(mockReply.send).toHaveBeenCalledWith({
        urn: 'urn:li:domain:1',
        name: 'Test Domain',
        description: 'Test Description'
      });
    });

    it('should handle domain with missing properties', async () => {
      // Mock data
      const mockRequest = {
        query: {
          urn: 'urn:li:domain:1'
        },
        log: {
          error: jest.fn()
        }
      } as any;

      const mockReply = {
        send: jest.fn().mockReturnThis(),
        status: jest.fn().mockReturnThis()
      } as any;

      const mockDomain = {
        urn: 'urn:li:domain:1',
        properties: undefined // Missing properties
      };

      // Set up mocks
      mockGetDomainByUrn.mockResolvedValue(mockDomain);

      // Call the controller
      await getDomainByUrnController(mockRequest, mockReply);

      // Assertions
      expect(mockGetDomainByUrn).toHaveBeenCalledWith(mockRequest, 'urn:li:domain:1');
      expect(mockReply.send).toHaveBeenCalledWith({
        urn: 'urn:li:domain:1',
        name: '', // Default empty string
        description: '' // Default empty string
      });
    });

    it('should return 404 when domain is not found', async () => {
      // Mock data
      const mockRequest = {
        query: {
          urn: 'urn:li:domain:nonexistent'
        },
        log: {
          error: jest.fn()
        }
      } as any;

      const mockReply = {
        send: jest.fn().mockReturnThis(),
        status: jest.fn().mockReturnThis()
      } as any;

      // Let's modify the controller implementation for this test
      const originalGetDomainByUrn = getDomainByUrn;

      // Replace the implementation temporarily
      (getDomainByUrn as jest.Mock).mockImplementation(async () => {
        // Return a domain that will be treated as falsy in the controller
        return undefined as any;
      });

      // Call the controller
      await getDomainByUrnController(mockRequest, mockReply);

      // Assertions
      expect(mockReply.status).toHaveBeenCalledWith(404);
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Domain not found' });

      // Restore the original implementation
      (getDomainByUrn as jest.Mock).mockImplementation(originalGetDomainByUrn);
    });

    it('should handle errors properly', async () => {
      // Mock data
      const mockRequest = {
        query: {
          urn: 'urn:li:domain:1'
        },
        log: {
          error: jest.fn()
        }
      } as any;

      const mockReply = {
        send: jest.fn().mockReturnThis(),
        status: jest.fn().mockReturnThis()
      } as any;

      const mockError = new Error('Test error');
      (mockError as any).details = ['Error detail'];

      // Set up mocks
      mockGetDomainByUrn.mockRejectedValue(mockError);

      // Call the controller
      await getDomainByUrnController(mockRequest, mockReply);

      // Assertions
      expect(mockRequest.log.error).toHaveBeenCalled();
      expect(mockReply.status).toHaveBeenCalledWith(500);
      expect(mockReply.send).toHaveBeenCalledWith({
        error: 'Test error',
        details: ['Error detail']
      });
    });

    it('should handle errors with empty message', async () => {
      // Mock data
      const mockRequest = {
        query: {
          urn: 'urn:li:domain:1'
        },
        log: {
          error: jest.fn()
        }
      } as any;

      const mockReply = {
        send: jest.fn().mockReturnThis(),
        status: jest.fn().mockReturnThis()
      } as any;

      // Create an error with empty message
      const error = new Error('');

      // Set up mocks
      mockGetDomainByUrn.mockRejectedValue(error);

      // Call the controller
      await getDomainByUrnController(mockRequest, mockReply);

      // Assertions
      expect(mockReply.status).toHaveBeenCalledWith(500);
      expect(mockReply.send).toHaveBeenCalledWith({
        error: 'Failed to fetch domain', // Default message
        details: undefined
      });
    });
  });
});
