{{- define "datahub-integration.fullname" -}}
{{- printf "%s-%s" .Release.Name .Chart.Name | trunc 63 | trimSuffix "-" -}}
{{- end }}

{{- define "datahub-integration.labels" -}}
helm.sh/chart: {{ .Chart.Name }}-{{ .Chart.Version }}
app.kubernetes.io/name: {{ .Chart.Name }}
app.kubernetes.io/instance: {{ .Release.Name }}
app.kubernetes.io/version: {{ .Chart.AppVersion }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{- define "datahub-integration.selectorLabels" -}}
app.kubernetes.io/name: {{ .Chart.Name }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{- define "datahub-integration.secureValidation" -}}
{{- if and (eq .Values.env.DATAHUB_SECURE "true") (not .Values.datahubTokenSecret.name) }}
{{- fail "DATAHUB_SECURE is true but datahubTokenSecret.name is not set. Please provide a secret name for the DataHub token." }}
{{- end }}
{{- if and (eq .Values.env.DATAHUB_SECURE "true") (not .Values.datahubTokenSecret.key) }}
{{- fail "DATAHUB_SECURE is true but datahubTokenSecret.key is not set. Please provide a secret key for the DataHub token." }}
{{- end }}
{{- end }} 