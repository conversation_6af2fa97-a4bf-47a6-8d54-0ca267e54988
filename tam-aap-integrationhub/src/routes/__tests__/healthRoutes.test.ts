import fastify, { FastifyInstance } from 'fastify';
import healthRoutes from '../healthRoutes.js';

describe('healthRoutes', () => {
  let app: FastifyInstance;
  beforeAll(async () => {
    app = fastify();
    app.register(healthRoutes);
    await app.ready();
  });
  afterAll(() => app.close());

  it('GET /health returns status ok', async () => {
    const res = await app.inject({ method: 'GET', url: '/health' });
    expect(res.statusCode).toBe(200);
    expect(res.json()).toEqual({ status: 'ok' });
  });

  it('GET / returns welcome message', async () => {
    const res = await app.inject({ method: 'GET', url: '/' });
    expect(res.statusCode).toBe(200);
    expect(res.json()).toEqual({ message: 'Welcome to the Integration Hub!' });
  });
}); 