{"name": "datahub-glossary-proxy", "version": "1.0.0", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon --exec 'tsc && node dist/index.js'", "format": "prettier --write . --loglevel debug", "test": "jest", "test:coverage": "jest --coverage"}, "dependencies": {"@fastify/swagger": "^9.5.0", "@fastify/swagger-ui": "^5.2.2", "@fastify/type-provider-typebox": "^5.1.0", "@sinclair/typebox": "^0.34.33", "dotenv": "^16.5.0", "fastify": "^5.3.2", "graphql": "^16.8.1", "mercurius": "^16.1.0", "node-fetch": "^2.7.0"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^22.15.3", "@types/node-fetch": "^2.6.12", "jest": "^29.7.0", "nodemon": "^3.0.3", "pino-pretty": "^13.0.0", "prettier": "^3.5.3", "ts-jest": "^29.1.2", "typescript": "^5.0.0"}}