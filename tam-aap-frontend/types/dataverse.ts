export interface GlossaryItem {
  id: string;
  title: string;
  description: string;
  domains: string[];
  acronym: string;
  owningEntity: string;
  synonyms: string[];
  lastUpdated: string;
  reviewedBy: {
    id: string;
    department: string;
    name: string;
  }[];
}

export interface GlossaryItemsQueryParams {
  search?: string;
  domain?: string;
  initialLetter?: string;
  maxItemsPerCharacters?: number;
}

export interface GlossaryItemsResponse {
  offset: number;
  limit: number;
  total: number;
  results: GlossaryItem[];
}

export interface MetricsItem {
  id: string;
  title: string;
  description: string;
  domains: string[];
  acronym: string;
  owningEntity: string;
  unit: string;
  endorsedBy: string[];
  formula: string;
  reviewedBy: {
    id: string;
    department: string;
    name: string;
  }[];
}

export interface MetricsItemsQueryParams {
  search?: string;
  endorsedBy?: string;
  owningEntity?: string;
  domain?: string;
}

export interface MetricsItemsResponse {
  offset: number;
  limit: number;
  total: number;
  results: MetricsItem[];
}
