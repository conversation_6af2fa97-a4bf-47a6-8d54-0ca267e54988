{"info": {"_postman_id": "70953a9c-227e-493f-9174-e78a8827e7cb", "name": "Tam AAP Backend", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "38811471"}, "item": [{"name": "glossary", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "    pm.expect(pm.response.code).to.equal(200);", "});", "", "pm.test(\"Validate the response schema for id, title, description, and domains\", function () {", "    const responseData = pm.response.json();", "", "    pm.expect(responseData).to.be.an('object');", "    ", "    // Validate the existence of id, title, description, and domains", "    responseData.results.forEach(function(result) {", "        pm.expect(result.id).to.exist.and.to.be.a('string');", "        pm.expect(result.title).to.exist.and.to.be.a('string');", "        pm.expect(result.description).to.exist.and.to.be.a('string');", "        pm.expect(result.domains).to.exist.and.to.be.a('Array');", "        ", "        result.domains.forEach(function(domain) {", "            pm.expect(domain).to.be.a('string');", "        });", "    });", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{tam_portal_url}}/glossary", "host": ["{{tam_portal_url}}"], "path": ["glossary"], "query": [{"key": "search", "value": "Code sharing", "disabled": true}, {"key": "domainName", "value": "Flight%20Operations", "disabled": true}, {"key": "kind", "value": "extended", "disabled": true}, {"key": "exactSearchMatch", "value": "true", "disabled": true}, {"key": "initialLetter", "value": "c", "disabled": true}, {"key": "domain", "value": "<PERSON><PERSON><PERSON>", "disabled": true}, {"key": "offset", "value": "0", "disabled": true}, {"key": "limit", "value": "3", "disabled": true}, {"key": "maxItemsPerCharacters", "value": "1", "disabled": true}]}}, "response": []}, {"name": "glossary with maxItemsPerCharacters", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "    pm.expect(pm.response.code).to.equal(200);", "});", "", "pm.test(\"Validate the response schema for id, title, description, and domains\", function () {", "    const responseData = pm.response.json();", "", "    pm.expect(responseData).to.be.an('object');", "    ", "    // Validate the existence of id, title, description, and domains", "    responseData.results.forEach(function(result) {", "        pm.expect(result.id).to.exist.and.to.be.a('string');", "        pm.expect(result.title).to.exist.and.to.be.a('string');", "        pm.expect(result.description).to.exist.and.to.be.a('string');", "        pm.expect(result.domains).to.exist.and.to.be.a('Array');", "        ", "        result.domains.forEach(function(domain) {", "            pm.expect(domain).to.be.a('string');", "        });", "    });", "});", "", "pm.test(\"Each initial character in title has at most maxItemsPerCharacters items\", function () {", "    const responseData = pm.response.json();", "    const url = pm.request.url.toString();", "    const urlParams = new URLSearchParams(url.split('?')[1]);", "", "    const maxItems = parseInt(urlParams.get('maxItemsPerCharacters'), 10);", "    pm.expect(maxItems, \"maxItemsPerCharacters should be a number\").to.be.a('number');", "", "    // Count occurrences of each initial character", "    const charCounts = {};", "", "    responseData.results.forEach((item) => {", "        const firstChar = item.title.trim().charAt(0).toUpperCase();", "        charCounts[firstChar] = (charCounts[firstChar] || 0) + 1;", "    });", "", "    // Assert each count is <= maxItems", "    Object.keys(charCounts).forEach((char) => {", "        pm.expect(charCounts[char], `Too many items for initial letter '${char}'`).to.be.at.most(maxItems);", "    });", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{tam_portal_url}}/glossary?maxItemsPerCharacters=1", "host": ["{{tam_portal_url}}"], "path": ["glossary"], "query": [{"key": "search", "value": "Code sharing", "disabled": true}, {"key": "domainName", "value": "Flight%20Operations", "disabled": true}, {"key": "kind", "value": "extended", "disabled": true}, {"key": "exactSearchMatch", "value": "true", "disabled": true}, {"key": "initialLetter", "value": "c", "disabled": true}, {"key": "domain", "value": "<PERSON><PERSON><PERSON>", "disabled": true}, {"key": "offset", "value": "0", "disabled": true}, {"key": "limit", "value": "3", "disabled": true}, {"key": "maxItemsPerCharacters", "value": "1"}]}}, "response": []}, {"name": "glossary with initial letter", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "    pm.expect(pm.response.code).to.equal(200);", "});", "", "pm.test(\"Validate the response schema for id, title, description, and domains\", function () {", "    const responseData = pm.response.json();", "", "    pm.expect(responseData).to.be.an('object');", "    ", "    // Validate the existence of id, title, description, and domains", "    responseData.results.forEach(function(result) {", "        pm.expect(result.id).to.exist.and.to.be.a('string');", "        pm.expect(result.title).to.exist.and.to.be.a('string');", "        pm.expect(result.description).to.exist.and.to.be.a('string');", "        pm.expect(result.domains).to.exist.and.to.be.a('Array');", "        ", "        result.domains.forEach(function(domain) {", "            pm.expect(domain).to.be.a('string');", "        });", "    });", "});", "", "// Test to check that all titles start with the initial letter specified in the query parameter", "pm.test(\"All titles start with the initial letter used in the query parameter\", function () {", "    const initialLetter = pm.request.url.query.get('initialLetter');", "    const responseData = pm.response.json();", "    const titles = responseData.results.map(result => result.title);", "", "    titles.forEach(title => {", "        pm.expect(title.charAt(0).toLowerCase()).to.equal(initialLetter.toLowerCase());", "    });", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{tam_portal_url}}/glossary?initialLetter=p", "host": ["{{tam_portal_url}}"], "path": ["glossary"], "query": [{"key": "search", "value": "Code sharing", "disabled": true}, {"key": "domainName", "value": "Flight%20Operations", "disabled": true}, {"key": "kind", "value": "extended", "disabled": true}, {"key": "exactSearchMatch", "value": "true", "disabled": true}, {"key": "initialLetter", "value": "p"}, {"key": "domain", "value": "<PERSON><PERSON><PERSON>", "disabled": true}, {"key": "offset", "value": "0", "disabled": true}, {"key": "limit", "value": "10", "disabled": true}]}}, "response": []}, {"name": "glossary with domain search", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "    pm.expect(pm.response.code).to.equal(200);", "});", "", "pm.test(\"Validate the response schema for id, title, description, and domains\", function () {", "    const responseData = pm.response.json();", "", "    pm.expect(responseData).to.be.an('object');", "    ", "    // Validate the existence of id, title, description, and domains", "    responseData.results.forEach(function(result) {", "        pm.expect(result.id).to.exist.and.to.be.a('string');", "        pm.expect(result.title).to.exist.and.to.be.a('string');", "        pm.expect(result.description).to.exist.and.to.be.a('string');", "        pm.expect(result.domains).to.exist.and.to.be.a('Array');", "        ", "        result.domains.forEach(function(domain) {", "            pm.expect(domain).to.be.a('string');", "        });", "    });", "});", "", "pm.test(\"All results contain the specified domain in 'domains'\", function () {", "    const responseData = pm.response.json();", "    const url = pm.request.url.toString();", "    const urlParams = new URLSearchParams(url.split('?')[1]);", "", "    const requestedDomain = decodeURIComponent(urlParams.get('domain') || '').toLowerCase();", "    pm.expect(requestedDomain).to.not.be.empty;", "", "    responseData.results.forEach((item, index) => {", "        const domains = item.domains.map(d => d.toLowerCase());", "        const containsDomain = domains.includes(requestedDomain);", "        pm.expect(containsDomain, `Result at index ${index} should include domain '${requestedDomain}'`).to.be.true;", "    });", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{tam_portal_url}}/glossary?domain=Flight Operations", "host": ["{{tam_portal_url}}"], "path": ["glossary"], "query": [{"key": "search", "value": "Code sharing", "disabled": true}, {"key": "domainName", "value": "Flight%20Operations", "disabled": true}, {"key": "kind", "value": "extended", "disabled": true}, {"key": "exactSearchMatch", "value": "true", "disabled": true}, {"key": "initialLetter", "value": "p", "disabled": true}, {"key": "domain", "value": "Flight Operations"}, {"key": "offset", "value": "0", "disabled": true}, {"key": "limit", "value": "10", "disabled": true}]}}, "response": []}, {"name": "glossary with search", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "    pm.expect(pm.response.code).to.equal(200);", "});", "", "pm.test(\"Validate the response schema for id, title, description, and domains\", function () {", "    const responseData = pm.response.json();", "", "    pm.expect(responseData).to.be.an('object');", "    ", "    // Validate the existence of id, title, description, and domains", "    responseData.results.forEach(function(result) {", "        pm.expect(result.id).to.exist.and.to.be.a('string');", "        pm.expect(result.title).to.exist.and.to.be.a('string');", "        pm.expect(result.description).to.exist.and.to.be.a('string');", "        pm.expect(result.domains).to.exist.and.to.be.a('Array');", "        ", "        result.domains.forEach(function(domain) {", "            pm.expect(domain).to.be.a('string');", "        });", "    });", "});", "", "pm.test(\"Search term exists in title or description\", function () {", "    const responseData = pm.response.json();", "    const url = pm.request.url.toString();", "    const urlParams = new URLSearchParams(url.split('?')[1]);", "", "    const searchTerm = decodeURIComponent(urlParams.get('search') || '').toLowerCase();", "", "    pm.expect(searchTerm).to.not.be.empty;", "", "    responseData.results.forEach((item, index) => {", "        const title = item.title.toLowerCase();", "        const description = item.description.toLowerCase();", "", "        const match = title.includes(searchTerm) || description.includes(searchTerm);", "", "        pm.expect(match, `Search term '${searchTerm}' should be in title or description of item at index ${index}`).to.be.true;", "    });", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{tam_portal_url}}/glossary?search=Commercial air transport&exactSearchMatch=true", "host": ["{{tam_portal_url}}"], "path": ["glossary"], "query": [{"key": "search", "value": "Commercial air transport"}, {"key": "domainName", "value": "Flight%20Operations", "disabled": true}, {"key": "kind", "value": "extended", "disabled": true}, {"key": "exactSearchMatch", "value": "true"}, {"key": "initialLetter", "value": "p", "disabled": true}, {"key": "domain", "value": "Flight Operations", "disabled": true}, {"key": "offset", "value": "0", "disabled": true}, {"key": "limit", "value": "10", "disabled": true}]}}, "response": []}, {"name": "glossary id", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "", "pm.test(\"Response time is less than 200ms\", function () {", "  pm.expect(pm.response.responseTime).to.be.below(200);", "});", "", "", "pm.test(\"Response has the required fields\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData).to.have.property('id');", "    pm.expect(responseData).to.have.property('title');", "    pm.expect(responseData).to.have.property('description');", "    pm.expect(responseData).to.have.property('domains');", "    pm.expect(responseData).to.have.property('lastUpdated');", "    pm.expect(responseData).to.have.property('owningEntity');", "    pm.expect(responseData).to.have.property('acronym');", "    pm.expect(responseData).to.have.property('synonyms');", "    pm.expect(responseData).to.have.property('reviewedBy');", "});", "", "", "pm.test(\"ID is a non-empty string\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData.id).to.be.a('string').and.to.have.lengthOf.at.most(1, \"ID should not be empty\");", "});", "", "", "pm.test(\"Domains is an array with at least one element\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData.domains).to.be.an('array').and.to.have.lengthOf.at.least(1);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{tam_portal_url}}/glossary/4bd6a4d381b526da9a4d3bead188637d", "host": ["{{tam_portal_url}}"], "path": ["glossary", "4bd6a4d381b526da9a4d3bead188637d"], "query": [{"key": "offset", "value": "0", "disabled": true}, {"key": "limit", "value": "10", "disabled": true}]}}, "response": []}, {"name": "metrics id", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", "", "", "pm.test(\"Content-Type header is application/json\", function () {", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/json\");", "});", "", "", "pm.test(\"Validate the response schema for required fields\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData.id).to.exist;", "    pm.expect(responseData.title).to.exist;", "    pm.expect(responseData.description).to.exist;", "    pm.expect(responseData.domains).to.exist;", "    pm.expect(responseData.unit).to.exist;", "    pm.expect(responseData.owningEntity).to.exist;", "    pm.expect(responseData.endorsedBy).to.exist;", "    pm.expect(responseData.acronym).to.exist;", "    pm.expect(responseData.reviewedBy).to.exist;", "    pm.expect(responseData.formula).to.exist;", "});", "", "", "pm.test(\"Response time is less than 200ms\", function () {", "  pm.expect(pm.response.responseTime).to.be.below(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{tam_portal_url}}/metrics/5b43a5fb61575095e2d575ac91a1f329", "host": ["{{tam_portal_url}}"], "path": ["metrics", "5b43a5fb61575095e2d575ac91a1f329"]}}, "response": []}, {"name": "metrics", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "", "pm.test(\"Response time is less than 200ms\", function () {", "  pm.expect(pm.response.responseTime).to.be.below(200);", "});", "", "", "pm.test(\"Response has the required fields\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData).to.have.property('results').that.is.an('array');", "", "    responseData.results.forEach(function(result) {", "        pm.expect(result).to.have.property('id').that.is.a('string');", "        pm.expect(result).to.have.property('title').that.is.a('string');", "        pm.expect(result).to.have.property('description').that.is.a('string');", "        pm.expect(result).to.have.property('domains').that.is.an('array');", "        pm.expect(result).to.have.property('unit').that.is.a('string');", "        pm.expect(result).to.have.property('owningEntity').that.is.a('string');", "        pm.expect(result).to.have.property('endorsedBy').that.is.an('array');", "        pm.expect(result).to.have.property('acronym').that.is.a('string');", "    });", "});", "", "", "pm.test(\"Id, title, and description are non-empty strings\", function () {", "  const responseData = pm.response.json();", "  ", "  pm.expect(responseData).to.be.an('object');", "  responseData.results.forEach(function(result) {", "    pm.expect(result.id).to.be.a('string').and.to.have.lengthOf.at.least(1, \"Id should not be empty\");", "    pm.expect(result.title).to.be.a('string').and.to.have.lengthOf.at.least(1, \"Title should not be empty\");", "    pm.expect(result.description).to.be.a('string').and.to.have.lengthOf.at.least(1, \"Description should not be empty\");", "  });", "});", "", "", "pm.test(\"Domains and endorsedBy are non-empty arrays in each result\", function () {", "    const responseData = pm.response.json();", "    ", "    responseData.results.forEach((item, index) => {", "        pm.expect(item.domains, `Missing or invalid 'domains' in item at index ${index}`).to.be.an('array').that.is.not.empty;", "        pm.expect(item.endorsedBy, `Missing or invalid 'endorsedBy' in item at index ${index}`).to.be.an('array').that.is.not.empty;", "    });", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{tam_portal_url}}/metrics", "host": ["{{tam_portal_url}}"], "path": ["metrics"], "query": [{"key": "offset", "value": "0", "disabled": true}, {"key": "limit", "value": "100", "disabled": true}, {"key": "kind", "value": "extended", "disabled": true}, {"key": "search", "value": "Air taxi operations", "disabled": true}, {"key": "exactSearchMatch", "value": "true", "disabled": true}, {"key": "<PERSON><PERSON><PERSON>", "value": "Safety Committee", "disabled": true}, {"key": "owningEntity", "value": "Hamad International Airport", "disabled": true}]}}, "response": []}, {"name": "metrics owningEntity", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "", "pm.test(\"Response time is less than 200ms\", function () {", "  pm.expect(pm.response.responseTime).to.be.below(200);", "});", "", "", "pm.test(\"Response has the required fields\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData).to.have.property('results').that.is.an('array');", "", "    responseData.results.forEach(function(result) {", "        pm.expect(result).to.have.property('id').that.is.a('string');", "        pm.expect(result).to.have.property('title').that.is.a('string');", "        pm.expect(result).to.have.property('description').that.is.a('string');", "        pm.expect(result).to.have.property('domains').that.is.an('array');", "        pm.expect(result).to.have.property('unit').that.is.a('string');", "        pm.expect(result).to.have.property('owningEntity').that.is.a('string');", "        pm.expect(result).to.have.property('endorsedBy').that.is.an('array');", "        pm.expect(result).to.have.property('acronym').that.is.a('string');", "    });", "});", "", "", "pm.test(\"Id, title, and description are non-empty strings\", function () {", "  const responseData = pm.response.json();", "  ", "  pm.expect(responseData).to.be.an('object');", "  responseData.results.forEach(function(result) {", "    pm.expect(result.id).to.be.a('string').and.to.have.lengthOf.at.least(1, \"Id should not be empty\");", "    pm.expect(result.title).to.be.a('string').and.to.have.lengthOf.at.least(1, \"Title should not be empty\");", "    pm.expect(result.description).to.be.a('string').and.to.have.lengthOf.at.least(1, \"Description should not be empty\");", "  });", "});", "", "", "pm.test(\"Domains and endorsedBy are non-empty arrays in each result\", function () {", "    const responseData = pm.response.json();", "    ", "    responseData.results.forEach((item, index) => {", "        pm.expect(item.domains, `Missing or invalid 'domains' in item at index ${index}`).to.be.an('array').that.is.not.empty;", "        pm.expect(item.endorsedBy, `Missing or invalid 'endorsedBy' in item at index ${index}`).to.be.an('array').that.is.not.empty;", "    });", "});", "", "pm.test(\"All results match the 'owningEntity' query\", function () {", "    const responseData = pm.response.json();", "    const requestUrl = pm.request.url.toString();", "", "    // Extract the owningEntity query parameter value", "    const owningEntityParam = decodeURIComponent(requestUrl.match(/[?&]owningEntity=([^&]+)/)?.[1] || '');", "", "    pm.expect(owningEntityParam, \"'owningEntity' query parameter must be present\").to.not.be.empty;", "", "    responseData.results.forEach((item, index) => {", "        pm.expect(item.owningEntity, `Missing 'owningEntity' in result at index ${index}`).to.exist;", "        pm.expect(item.owningEntity, `owningEntity mismatch at index ${index}`).to.eql(owningEntityParam);", "    });", "});", "", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{tam_portal_url}}/metrics?owningEntity=Hamad International Airport", "host": ["{{tam_portal_url}}"], "path": ["metrics"], "query": [{"key": "offset", "value": "0", "disabled": true}, {"key": "limit", "value": "100", "disabled": true}, {"key": "kind", "value": "extended", "disabled": true}, {"key": "search", "value": "Air taxi operations", "disabled": true}, {"key": "exactSearchMatch", "value": "true", "disabled": true}, {"key": "<PERSON><PERSON><PERSON>", "value": "Safety Committee", "disabled": true}, {"key": "owningEntity", "value": "Hamad International Airport"}]}}, "response": []}, {"name": "metrics search by endorsedBy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", "", "", "pm.test(\"Response time is less than 200ms\", function () {", "  pm.expect(pm.response.responseTime).to.be.below(200);", "});", "", "", "pm.test(\"All results are endorsed by the queried 'endorsedBy' entity\", function () {", "    const responseData = pm.response.json();", "    const requestUrl = pm.request.url.toString();", "", "    // Extract the value of the endorsedBy query parameter", "    const endorsedByParam = decodeURIComponent(requestUrl.match(/[?&]endorsedBy=([^&]+)/)?.[1] || '');", "", "    pm.expect(endorsedByParam, \"endorsedBy parameter must be present in query\").to.not.be.empty;", "", "    responseData.results.forEach((item, index) => {", "        pm.expect(item.endorsedBy, `Missing 'endorsedBy' array in item at index ${index}`).to.be.an('array');", "        pm.expect(item.endorsedBy, `Item at index ${index} does not include '${endorsedByParam}'`).to.include(endorsedByParam);", "    });", "});", "", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{tam_portal_url}}/metrics?endorsedBy=Safety Committee", "host": ["{{tam_portal_url}}"], "path": ["metrics"], "query": [{"key": "offset", "value": "0", "disabled": true}, {"key": "limit", "value": "100", "disabled": true}, {"key": "kind", "value": "extended", "disabled": true}, {"key": "search", "value": "Air taxi operations", "disabled": true}, {"key": "exactSearchMatch", "value": "true", "disabled": true}, {"key": "<PERSON><PERSON><PERSON>", "value": "Safety Committee"}, {"key": "owningEntity", "value": "Hamad International Airport", "disabled": true}]}}, "response": []}]}