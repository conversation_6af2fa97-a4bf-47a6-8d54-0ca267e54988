import { domainToResponse } from '../domainTransformer.js';
import { DomainEntity } from '../../types/domainTypes.js';

describe('domainTransformer', () => {
  describe('domainToResponse', () => {
    it('should transform a domain entity to a response object', () => {
      // Mock domain entity
      const mockDomainEntity: DomainEntity = {
        urn: 'urn:li:domain:1',
        properties: {
          name: 'Test Domain',
          description: 'Test Description'
        }
      };

      // Expected response
      const expectedResponse = {
        urn: 'urn:li:domain:1',
        name: 'Test Domain',
        description: 'Test Description'
      };

      // Call the transformer
      const result = domainToResponse(mockDomainEntity);

      // Assertions
      expect(result).toEqual(expectedResponse);
    });

    it('should handle missing properties', () => {
      // Mock domain entity with missing properties
      const mockDomainEntity: DomainEntity = {
        urn: 'urn:li:domain:1',
        properties: undefined
      };

      // Expected response
      const expectedResponse = {
        urn: 'urn:li:domain:1',
        name: '',
        description: ''
      };

      // Call the transformer
      const result = domainToResponse(mockDomainEntity);

      // Assertions
      expect(result).toEqual(expectedResponse);
    });

    it('should handle missing name and description', () => {
      // Mock domain entity with missing name and description
      const mockDomainEntity: DomainEntity = {
        urn: 'urn:li:domain:1',
        properties: {}
      };

      // Expected response
      const expectedResponse = {
        urn: 'urn:li:domain:1',
        name: '',
        description: ''
      };

      // Call the transformer
      const result = domainToResponse(mockDomainEntity);

      // Assertions
      expect(result).toEqual(expectedResponse);
    });

    it('should handle missing name', () => {
      // Mock domain entity with missing name
      const mockDomainEntity: DomainEntity = {
        urn: 'urn:li:domain:1',
        properties: {
          description: 'Test Description'
        }
      };

      // Expected response
      const expectedResponse = {
        urn: 'urn:li:domain:1',
        name: '',
        description: 'Test Description'
      };

      // Call the transformer
      const result = domainToResponse(mockDomainEntity);

      // Assertions
      expect(result).toEqual(expectedResponse);
    });

    it('should handle missing description', () => {
      // Mock domain entity with missing description
      const mockDomainEntity: DomainEntity = {
        urn: 'urn:li:domain:1',
        properties: {
          name: 'Test Domain'
        }
      };

      // Expected response
      const expectedResponse = {
        urn: 'urn:li:domain:1',
        name: 'Test Domain',
        description: ''
      };

      // Call the transformer
      const result = domainToResponse(mockDomainEntity);

      // Assertions
      expect(result).toEqual(expectedResponse);
    });
  });
});
