// Utility functions for domain filtering

/**
 * Filters domain results by the initial letter of the name.
 * @param results Array of domain search results (raw from DataHub)
 * @param initialLetter The letter to filter by
 * @returns Filtered array
 */
export function filterByInitialLetter(results: any[], initialLetter: string): any[] {
  if (!initialLetter || typeof initialLetter !== 'string') return results;
  const initial = initialLetter[0].toLowerCase();
  return results.filter(r => r.name && r.name[0]?.toLowerCase() === initial);
}

/**
 * Filters domain results by substring search match on name or description (case-insensitive).
 * @param results Array of domain search results (flat structure)
 * @param searchTerm The search term to match as a substring
 * @returns Filtered array
 */
export function filterByExactSearchMatch(results: any[], searchTerm: string): any[] {
  if (!searchTerm || typeof searchTerm !== 'string') return results;
  const searchLower = searchTerm.trim().toLowerCase();
  return results.filter((r: any) =>
    (typeof r.name === 'string' && r.name.trim().toLowerCase().includes(searchLower)) ||
    (typeof r.description === 'string' && r.description.trim().toLowerCase().includes(searchLower))
  );
}

// Registry of available domain filters
export const domainFilterRegistry: Record<string, (results: any[], value: any, filters: Record<string, any>) => any[]> = {
  initialLetter: (results: any[], value: any) => filterByInitialLetter(results, value),
  exactSearchMatch: (results: any[], value: any, filters: Record<string, any>) => {
    // Only apply if filters.search is present
    if (filters && filters.search) {
      return filterByExactSearchMatch(results, filters.search);
    }
    return results;
  },
  // Add more filters here as needed
};

/**
 * Applies all registered filters to the results based on the filters object.
 * @param results Array of domain search results
 * @param filters Object containing filter values
 * @returns Filtered array
 */
export function applyDomainFilters(results: any[], filters: Record<string, any>): any[] {
  let filtered = results;
  for (const key in filters) {
    if (domainFilterRegistry[key] && filters[key] !== undefined) {
      filtered = domainFilterRegistry[key](filtered, filters[key], filters);
    }
  }
  return filtered;
}