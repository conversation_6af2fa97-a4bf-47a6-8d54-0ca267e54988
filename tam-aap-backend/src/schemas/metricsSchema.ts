import { Type } from '@fastify/type-provider-typebox';
import { PaginationQuery } from './commonSchema';

export const metricsQuerySchema = Type.Object({
  search: Type.Optional(Type.String({
    description: 'A full-text search term. Matches against both the metric name and description. Partial and case-insensitive matches are supported. Example: searching for "departure" will return any metric or description containing "departure".',
    examples: ['Air taxi operations', 'Aircraft departures']
  })),
  domainName: Type.Optional(Type.String({
    description: 'Restrict results to metrics belonging to this domain. The domain name must match exactly (case-sensitive). Example: "Flight Operations".',
    examples: ['Flight Operations']
  })),
  exactSearchMatch: Type.Optional(Type.Boolean({
    description: 'If true, the search term will match as a substring in either the name or description (case-insensitive). If false or omitted, partial matches are allowed.',
    examples: [true, false]
  })),
  initialLetter: Type.Optional(Type.String({
    minLength: 1,
    maxLength: 1,
    description: 'Filter metrics whose name starts with this single letter (case-insensitive). For example, "A" will match "Aircraft departures".',
    examples: ['A', 'B']
  })),
  domain: Type.Optional(Type.String({
    description: 'Filter metrics by domain. This is an additional filter and can be combined with domainName.',
    examples: ['Flight Operations']
  })),
  offset: Type.Optional(Type.Integer({
    minimum: 0,
    description: 'The index of the first result to return (pagination offset). Use in combination with "limit" for paginated results. Default is 0.'
  })),
  limit: Type.Optional(Type.Integer({
    minimum: 1,
    description: 'The maximum number of results to return (pagination limit). If not provided, all results will be returned in batches of 1000 until all are fetched.'
  })),
  owningEntity: Type.Optional(Type.String({
    description: 'Filter metrics by owning entity (case-sensitive).',
    examples: ['Flight Operations']
  })),
  endorsedBy: Type.Optional(Type.String({
    description: 'Filter metrics by endorsed by (case-sensitive).',
    examples: ['Flight Operations Committee']
  }))
}, {
  description: 'Query parameters for searching metrics in DataHub. All fields are optional and can be combined for more specific searches.'
});

// Schema used for validation (includes customProperties)
export const metricsBodyValidationSchema = Type.Object({
  search: Type.Optional(Type.String()),
  domainName: Type.Optional(Type.String()),
  exactSearchMatch: Type.Optional(Type.Boolean()),
  initialLetter: Type.Optional(Type.String({ minLength: 1, maxLength: 1 })),
  domain: Type.Optional(Type.String()),
  customProperties: Type.Array(
    Type.Object({
      key: Type.String({ enum: ['type'] }),
      value: Type.String({ enum: ['metrics'] })
    }),
    { default: [{ key: 'type', value: 'metrics' }] }
  )
});

// Schema used for Swagger docs
export const metricsBodySwaggerSchema = Type.Object({
  search: Type.Optional(Type.String({
    description: 'A full-text search term. Matches against both the metric name and description. Partial and case-insensitive matches are supported. Example: searching for "departure" will return any metric or description containing "departure".',
    examples: ['Air taxi operations', 'Aircraft departures']
  })),
  domainName: Type.Optional(Type.String({
    description: 'Restrict results to metrics belonging to this domain. The domain name must match exactly (case-sensitive). Example: "Flight Operations".',
    examples: ['Flight Operations']
  })),
  exactSearchMatch: Type.Optional(Type.Boolean({
    description: 'If true, the search term will match as a substring in either the name or description (case-insensitive). If false or omitted, partial matches are allowed.',
    examples: [true, false]
  })),
  initialLetter: Type.Optional(Type.String({
    minLength: 1,
    maxLength: 1,
    description: 'Filter metrics whose name starts with this single letter (case-insensitive). For example, "A" will match "Aircraft departures".',
    examples: ['A', 'B']
  })),
  domain: Type.Optional(Type.String({
    description: 'Filter metrics by domain. This is an additional filter and can be combined with domainName.',
    examples: ['Flight Operations']
  }))
}, {
  description: 'Request body for searching metrics. All fields are optional and can be combined for more specific searches.'
  // To add request examples for Swagger, use the route schema (metricsRouteSchema) with the x-examples OpenAPI extension.
});

export const metricsResponseSchema = Type.Object({
  offset: Type.Integer(),
  limit: Type.Integer(),
  total: Type.Integer(),
  results: Type.Array(
    Type.Object({
      id: Type.String(),
      title: Type.String(),
      description: Type.String(),
      domains: Type.Array(Type.String(), { default: [] }),
      unit: Type.Optional(Type.String()),
      owningEntity: Type.Optional(Type.String()),
      endorsedBy: Type.Optional(Type.Array(Type.String())),
      acronym: Type.Optional(Type.String())
    })
  )
});

export const metricsRouteSchema = {
  tags: ['Metrics'],
  summary: 'Search airport metrics',
  description: 'Search for airport metrics with flexible filtering options including domain, department, and more.',
  querystring: metricsQuerySchema,
  response: {
    200: metricsResponseSchema
  },
  'x-examples': {
    'Search metrics': {
      value: {
        search: 'Air taxi operations',
        domainName: 'Flight Operations',
        exactSearchMatch: true,
        initialLetter: 'A',
        domain: 'Flight Operations'
      }
    }
  }
};

export const metricsByIdRouteSchema = {
  tags: ['Metrics'],
  summary: 'Get metric by ID',
  description: 'Fetch a single metric by its ID.',
  params: Type.Object({
    id: Type.String({ description: 'The ID of the metric to fetch.' })
  }),
  response: {
    200: Type.Object({
      id: Type.String(),
      title: Type.String(),
      description: Type.String(),
      domains: Type.Array(Type.String()),
      unit: Type.Optional(Type.String()),
      owningEntity: Type.Optional(Type.String()),
      endorsedBy: Type.Optional(Type.Array(Type.String())),
      acronym: Type.Optional(Type.String()),
      reviewedBy: Type.Optional(Type.Array(Type.Object({
        id: Type.String(),
        department: Type.String(),
        name: Type.String()
      }))),
      formula: Type.Optional(Type.String())
    }),
    404: Type.Object({
      error: Type.String()
    }),
    502: Type.Object({
      error: Type.String()
    })
  }
};