{{- include "datahub-integration.secureValidation" . }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "datahub-integration.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "datahub-integration.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "datahub-integration.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "datahub-integration.selectorLabels" . | nindent 8 }}
      annotations:
        rollme: {{ .Values.annotations.rollme | quote }}
    spec:
      {{- if .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml .Values.imagePullSecrets | nindent 8 }}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - containerPort: {{ .Values.service.port }}
          envFrom:
            - configMapRef:
                name: {{ include "datahub-integration.fullname" . }}
          {{- if eq .Values.env.DATAHUB_SECURE "true" }}
          env:
            - name: DATAHUB_TOKEN
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.datahubTokenSecret.name }}
                  key: {{ .Values.datahubTokenSecret.key }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          {{- if .Values.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: {{ .Values.livenessProbe.path }}
              port: {{ .Values.livenessProbe.port }}
            initialDelaySeconds: 10
            periodSeconds: 10
          {{- end }}
          {{- if .Values.readinessProbe.enabled }}
          readinessProbe:
            httpGet:
              path: {{ .Values.readinessProbe.path }}
              port: {{ .Values.readinessProbe.port }}
            initialDelaySeconds: 5
            periodSeconds: 10
          {{- end }}
          {{- if and (eq .Values.env.DATAHUB_SECURE "true") (not .Values.datahubTokenSecret.name) }}
          {{- fail "DATAHUB_SECURE is true but datahubTokenSecret.name is not set. Please provide a secret name for the DataHub token." }}
          {{- end }}
          {{- if and (eq .Values.env.DATAHUB_SECURE "true") (not .Values.datahubTokenSecret.key) }}
          {{- fail "DATAHUB_SECURE is true but datahubTokenSecret.key is not set. Please provide a secret key for the DataHub token." }}
          {{- end }} 