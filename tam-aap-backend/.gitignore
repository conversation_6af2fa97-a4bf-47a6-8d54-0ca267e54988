# Environment variables
.env
.env.*
!.env.example

# Kubernetes and Docker files
kind/*

# Dependencies
node_modules/
yarn.lock
pnpm-lock.yaml

# Build outputs
dist/
build/
out/
.next/

# Testing
coverage/
.nyc_output/
test-results/
playwright-report/
.playwright/

# Editor files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea/
*.sublime-*
*.swp
*.swo

# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# OS files
.DS_Store
Thumbs.db
.directory
*~

# Temporary files
.tmp/
.temp/
temp/
tmp/

# Cache
.cache/
.npm
.eslintcache
.stylelintcache

# Local files
*.local

# values-dev.yaml
values-*.yaml