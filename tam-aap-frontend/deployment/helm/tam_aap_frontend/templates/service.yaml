apiVersion: v1
kind: Service
metadata:
  name: {{ include "tam-aap-frontend.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: {{ include "tam-aap-frontend.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.port }}
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: {{ include "tam-aap-frontend.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }} 