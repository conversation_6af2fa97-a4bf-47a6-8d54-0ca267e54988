// Utility functions for building GraphQL queries for glossary search

/**
 * Escapes a string for use in a GraphQL query.
 * @param str The string to escape
 * @returns The escaped string
 */
export function escapeGraphQLString(str: string): string {
  return str
    .replace(/\\/g, '\\\\')
    .replace(/"/g, '\\"')
    .replace(/\n/g, '\\n')
    .replace(/\r/g, '\\r')
    .replace(/\t/g, '\\t');
}

/**
 * Builds a GraphQL query string for searching glossary terms.
 * @param queryText The search text
 * @param start The start index for pagination
 * @param count The number of results to fetch
 * @param filtersClause The filters clause as a string (e.g., filters: [ ... ])
 * @returns GraphQL query string
 */
export function buildGlossarySearchQuery(queryText: string, start: number, count: number, filtersClause: string): string {
  const escapedQuery = escapeGraphQLString(queryText);
  return `{
    search(input: {
      type: GLOSSARY_TERM,
      query: "${escapedQuery}",
      start: ${start},
      count: ${count},
      ${filtersClause}
    }) {
      start
      count
      total
      searchResults {
        entity {
          urn
          ... on GlossaryTerm {
            name
            glossaryTermInfo {
              name
              description
              customProperties {
                key
                value
              }
            }
          }
        }
      }
    }
  }`;
}

/**
 * Builds a GraphQL query string for fetching a glossary term by URN.
 * @param urn The glossary term URN
 * @returns GraphQL query string
 */
export function buildGetGlossaryTermByUrnQuery(urn: string): string {
  const escapedUrn = escapeGraphQLString(urn);
  return `{
    glossaryTerm(urn: "${escapedUrn}") {
      urn
      name
      glossaryTermInfo {
        name
        description
        customProperties {
          key
          value
        }
      }
    }
  }`;
} 