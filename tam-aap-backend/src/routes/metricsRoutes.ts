import { FastifyPluginAsync } from 'fastify';
import { metrics<PERSON><PERSON><PERSON>, getMetricByIdHandler } from '../controllers/metricsController';
import { metricsRouteSchema, metricsBodyValidationSchema, metricsQuerySchema, metricsByIdRouteSchema } from '../schemas/metricsSchema';
import { Static } from '@sinclair/typebox';
import { Type } from '@fastify/type-provider-typebox';

// Types for request body and query
export type MetricsBody = Static<typeof metricsBodyValidationSchema>;
export type MetricsQuery = Static<typeof metricsQuerySchema>;

const metricsRoutes: FastifyPluginAsync = async (fastify) => {
  fastify.get<{
    Querystring: MetricsQuery;
  }>('/metrics', {
    schema: metricsRouteSchema,
    handler: metricsHandler,
  });

  fastify.get<{
    Params: { id: string };
  }>('/metrics/:id', {
    schema: metricsByIdRouteSchema,
    handler: getMetricByIdHandler,
  });
};

export default metricsRoutes; 