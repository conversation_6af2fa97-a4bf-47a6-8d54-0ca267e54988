import { ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";

interface Department {
  name: string;
}

interface DepartmentSidebarProps {
  expanded: boolean;
  toggleSidebar: () => void;
  departments: Department[];
  activeDepartment: string | null;
  onDepartmentClick: (department: string) => void;
}

export default function DepartmentSidebar({
  expanded,
  toggleSidebar,
  departments,
  activeDepartment,
  onDepartmentClick,
}: DepartmentSidebarProps) {
  return (
    <div className="relative flex-shrink-0 transition-all duration-300 h-full pr-6 pb-6">
      <div
        className={`h-full transition-all duration-300 ${
          expanded ? "w-62" : "w-12"
        }`}
      >
        <div className="flex flex-col h-full">
          <div className="space-y-2">
            <button
              className={`flex items-center w-full h-10 text-left px-4 rounded-md transition-colors ${
                activeDepartment === "All"
                  ? "bg-sidebar-primary text-sidebar-primary-foreground"
                  : "hover:bg-sidebar-accent/50 text-sidebar-foreground/80 hover:text-sidebar-foreground"
              }`}
              onClick={() => onDepartmentClick("All")}
              title="All Departments"
            >
              <div className="flex items-center justify-center ">All</div>
            </button>

            {departments.map((dept) => {
              return (
                <button
                  key={dept.name}
                  className={`flex items-center w-full h-10 text-left pl-2 rounded-md transition-colors text-xl ${
                    activeDepartment === dept.name
                      ? "bg-sidebar-primary text-sidebar-primary-foreground"
                      : "hover:bg-sidebar-accent/50 text-sidebar-foreground/80 hover:text-sidebar-foreground"
                  }`}
                  onClick={() => onDepartmentClick(dept.name)}
                  title={dept.name}
                >
                  <div
                    className={`flex items-center justify-center rounded-full overflow-hidden flex-shrink-0`}
                  >
                    <Image
                      src={"/svg/" + dept.name + ".svg"}
                      alt={dept.name}
                      width={32}
                      height={32}
                      className="object-contain"
                    />
                  </div>
                  {expanded && (
                    <span className="ml-2 truncate">{dept.name}</span>
                  )}
                </button>
              );
            })}
          </div>
        </div>
      </div>

      <div className="absolute right-0 top-0 bottom-0 h-full border-r border-sidebar-border flex items-center">
        <div
          onClick={toggleSidebar}
          className="absolute right-[-15px] top-[20px] transform bg-[#2C2F35] border border-gray-700 rounded-full p-1 cursor-pointer shadow-md hover:bg-[#3A3D45] transition-colors"
        >
          {expanded ? (
            <ChevronLeft className="h-5 w-5 text-white" />
          ) : (
            <ChevronRight className="h-5 w-5 text-white" />
          )}
        </div>
      </div>
    </div>
  );
}
