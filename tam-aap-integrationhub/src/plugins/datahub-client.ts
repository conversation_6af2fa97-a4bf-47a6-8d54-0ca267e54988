import fetch from 'node-fetch';
import fp from 'fastify-plugin';
import { FastifyPluginAsync } from 'fastify';
import { DATAHUB_GRAPHQL_ENDPOINT, DATAHUB_TOKEN } from '../configs/config.js';
import { DataHubClient, GraphQLResponse, DatahubClientOptions } from '../types/fastifyTypes.js';

const DATAHUB_SECURE = process.env.DATAHUB_SECURE === 'true';

const datahubClient: FastifyPluginAsync<DatahubClientOptions> = async (fastify, opts) => {
  const endpoint = opts.endpoint || DATAHUB_GRAPHQL_ENDPOINT;
  const token = opts.token || DATAHUB_TOKEN;
  const timeoutMs = opts.timeoutMs || 10000;

  fastify.decorateRequest('datahub', {
    getter(): DataHubClient {
      return {
        query: async <T>(query: string, variables: Record<string, any> = {}): Promise<GraphQLResponse<T>> => {
          const headers: Record<string, string> = { 'Content-Type': 'application/json' };
          if (DATAHUB_SECURE && token) headers['Authorization'] = `Bearer ${token}`;
          const controller = new AbortController();
          const timeout = setTimeout(() => controller.abort(), timeoutMs);

          try {
            const res = await fetch(endpoint, {
            method: 'POST',
            headers,
            body: JSON.stringify({ query, variables }),
              signal: controller.signal,
          });
          if (!res.ok) {
              fastify.log.error(`GraphQL request failed: ${res.status} ${res.statusText} - ${await res.text()} (endpoint: ${endpoint})`);
            throw new Error(`GraphQL request failed: ${res.status} ${res.statusText}`);
          }
          return res.json() as Promise<GraphQLResponse<T>>;
          } catch (err) {
            fastify.log.error({ err, endpoint, query }, 'DataHub GraphQL query error');
            throw err;
          } finally {
            clearTimeout(timeout);
          }
        },
      };
    },
  });
};

export default fp(datahubClient);