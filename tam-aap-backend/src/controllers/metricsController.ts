import { FastifyRequest, FastifyReply } from 'fastify';
import { searchMetrics } from '../services/metricsService';
import { transformMetricsResponse, TransformedMetricsResponse, TransformedMetricsItem } from '../transformers/metricsTransformer';
import { metricsQuerySchema } from '../schemas/metricsSchema';
import type { Static } from '@sinclair/typebox';
import axios from 'axios';
import { INTEGRATION_HUB_HOST, INTEGRATION_HUB_PORT } from '../configs/configs';

// Define types for request body and query
// These match the schemas
// If you want to export TransformedMetricsResponse, you can do so from the transformer if needed

type MetricsQuery = Static<typeof metricsQuerySchema>;

// Define interface for raw metrics response
interface RawMetricsResponse {
  urn: string;
  name: string;
  description: string;
  customProperties?: Array<{ key: string; value: string }>;
}

// Function to process custom properties for metrics
function processMetricsCustomProperties(raw: RawMetricsResponse): {
  domains: string[];
  unit?: string;
} {
  let domains: string[] = [];
  let unit: string | undefined;

  if (raw.customProperties && raw.customProperties.length > 0) {
    raw.customProperties.forEach(prop => {
      if (prop.key === 'departments' && prop.value) {
        domains = prop.value.split(',').map(s => s.trim());
      } else if (prop.key === 'unit') {
        unit = prop.value;
      }
    });
  }

  return { domains, unit };
}

export async function metricsHandler(
  request: FastifyRequest<{ Querystring: MetricsQuery }>,
  reply: FastifyReply
): Promise<TransformedMetricsResponse> {
  const { search, domainName, exactSearchMatch, initialLetter, domain, offset, limit, owningEntity, endorsedBy } = request.query;
  try {
    const raw = await searchMetrics({ search, domainName, offset, limit, exactSearchMatch, initialLetter });
    let data = transformMetricsResponse(raw);

    // Filter by domain if provided
    if (domain) {
      const filteredResults = data.results.filter(item => Array.isArray(item.domains) && item.domains.includes(domain));
      data = {
        ...data,
        results: filteredResults,
        total: filteredResults.length
      };
    }

    // Filter by owningEntity if provided
    if (owningEntity) {
      const filteredResults = data.results.filter(item => item.owningEntity === owningEntity);
      data = {
        ...data,
        results: filteredResults,
        total: filteredResults.length
      };
    }

    // Filter by endorsedBy if provided
    if (endorsedBy) {
      const filteredResults = data.results.filter(item => 
        item.endorsedBy && item.endorsedBy.includes(endorsedBy)
      );
      data = {
        ...data,
        results: filteredResults,
        total: filteredResults.length
      };
    }

    return data;
  } catch (err) {
    request.server.log.error(err);
    throw reply.code(502).send({ error: 'Failed to fetch metrics data' });
  }
}

export async function getMetricByIdHandler(
  request: FastifyRequest<{ Params: { id: string } }>,
  reply: FastifyReply
): Promise<TransformedMetricsItem> {
  const { id } = request.params;
  try {
    const urn = `urn:li:glossaryTerm:${id}`;
    const response = await axios.get(
      `http://${INTEGRATION_HUB_HOST}:${INTEGRATION_HUB_PORT}/glossary/urn?urn=${encodeURIComponent(urn)}`
    );
    const raw: RawMetricsResponse = response.data;

    const { domains, unit } = processMetricsCustomProperties(raw);

    // Extract all relevant custom properties
    let owningEntity: string | undefined = undefined;
    let endorsedBy: string[] | undefined = undefined;
    let acronym: string | undefined = undefined;
    let reviewedBy: Array<{ id: string; department: string; name: string }> | undefined = undefined;
    let formula: string | undefined = undefined;
    if (raw.customProperties && raw.customProperties.length > 0) {
      raw.customProperties.forEach(prop => {
        if (prop.key === 'owningEntity') owningEntity = prop.value;
        else if (prop.key === 'endorsedBy') endorsedBy = prop.value.split(',').map(s => s.trim());
        else if (prop.key === 'acronym') acronym = prop.value;
        else if (prop.key === 'reviewedBy' && prop.value) {
          reviewedBy = prop.value.split(',').map(reviewer => {
            const [id, department, name] = reviewer.split(':');
            return { id, department, name };
          });
        }
        else if (prop.key === 'formula') formula = prop.value;
      });
    }

    return {
      id: raw.urn.replace('urn:li:glossaryTerm:', ''),
      title: raw.name,
      description: raw.description,
      domains,
      unit,
      owningEntity,
      endorsedBy,
      acronym,
      reviewedBy,
      formula
    };
  } catch (err) {
    request.server.log.error(err);
    throw reply.code(502).send({ error: 'Failed to fetch metric' });
  }
}