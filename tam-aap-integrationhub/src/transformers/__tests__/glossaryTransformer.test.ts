import { glossaryTermToResponse } from '../glossaryTransformer.js';
import { Static } from '@sinclair/typebox';
import { GlossaryTerm, GlossaryTermEntity } from '../../types/glossaryTypes.js';

describe('glossaryTransformer', () => {
  it('should transform a GlossaryTermEntity to a GlossaryTerm', () => {
    const entity: GlossaryTermEntity = {
      urn: 'urn:li:glossaryTerm:1',
      name: 'Original Name',
      glossaryTermInfo: {
        name: 'Transformed Name',
        description: 'A description',
        customProperties: [
          { key: 'type', value: 'metrics' },
          { key: 'department', value: 'Flight Operations' }
        ]
      }
    };

    const result = glossaryTermToResponse(entity);
    expect(result).toEqual({
      urn: 'urn:li:glossaryTerm:1',
      name: 'Transformed Name',
      description: 'A description',
      customProperties: [
        { key: 'type', value: 'metrics' },
        { key: 'department', value: 'Flight Operations' }
      ]
    });
  });

  it('should handle empty customProperties', () => {
    const entity: GlossaryTermEntity = {
      urn: 'urn:li:glossaryTerm:2',
      name: 'Original Name',
      glossaryTermInfo: {
        name: 'Transformed Name',
        description: 'Another description',
        customProperties: []
      }
    };

    const result = glossaryTermToResponse(entity);
    expect(result).toEqual({
      urn: 'urn:li:glossaryTerm:2',
      name: 'Transformed Name',
      description: 'Another description',
      customProperties: []
    });
  });
}); 