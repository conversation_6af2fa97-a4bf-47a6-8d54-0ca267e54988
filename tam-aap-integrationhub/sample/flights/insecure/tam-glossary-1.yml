version: '1'
source: ICAO
owners:
  users:
    - datahub
url: 'https://www.icao.int/'
nodes:
  - name: Commercial air carriers
    description: Commercial air carriers
    knowledge_links:
      - label: Wiki link for classification
        url: 'https://en.wikipedia.org/wiki/Classification'
    terms:
      - name: Air taxi revenue flights
        description: On-demand, non-scheduled flights on short notice for the carriage by air of passengers, freight or mail, or any combination thereof for remuneration usually performed with smaller aircraft including helicopters (typically no more than 30 seats). Also includes any positioning flights required for the provision of the service.
        custom_properties:
          is_confidential: 'false'
          departments: 'Airfield, Flight Operations'
          detailedDescription: 'Air taxi revenue flights represent a flexible and on-demand air transportation service, crucial for connecting remote locations and providing quick access to business centers. These operations require careful coordination between airfield staff and flight operations teams to ensure efficient scheduling and safe execution.'
          type: 'metrics'
          unit: 'flights'
          reviewedBy: "u001:Flight Operations:Flight Operations Data Standards Committee,u005:Airfield:Airfield Data Standards Committee"
          owningEntity: "Flight Operations"
          acronym: "ATRF"
          endorsedBy: "Flight Operations Committee, Safety Committee"
          formula: "Sum of all air taxi revenue flights within the reporting period"
        domain: Flight Operations
      - name: Aircraft departures
        description: The number of take-offs of aircraft. For statistical purposes, departures are equal to the number of landings made or flight stages flown.
        custom_properties:
          is_confidential: 'false'
          departments: 'Airfield, Flight Operations'
          detailedDescription: 'Aircraft departures are a fundamental metric for measuring airport and airline operational capacity. Each departure requires careful coordination between airfield operations and flight operations teams to ensure safe and efficient takeoff procedures.'
          type: 'metrics'
          unit: 'departures'
          reviewedBy: "u001:Flight Operations:Flight Operations Data Standards Committee,u005:Airfield:Airfield Data Standards Committee"
          owningEntity: "Flight Operations"
          acronym: "ADP"
          endorsedBy: "Flight Operations Committee, Technical Committee"
          formula: "Count of all aircraft take-offs within the reporting period"
        domain: Flight Operations
      - name: Aircraft hours
        description: Aircraft hours are based on "block-to-block" time (i.e. from the moment the aircraft is pushed back from the gate or starts taxiing from its parking stand for take-off to the moment it comes to a final stop at a gate or parking stand after landing); also known as block time.
        custom_properties:
          is_confidential: 'false'
          departments: 'Airfield, Flight Operations'
          detailedDescription: 'Aircraft hours, measured as block-to-block time, are essential for operational planning and maintenance scheduling. This metric helps both airfield and flight operations teams optimize aircraft utilization and ensure proper maintenance intervals.'
          type: 'metrics'
          unit: 'hours'
          reviewedBy: "u001:Flight Operations:Flight Operations Data Standards Committee,u005:Airfield:Airfield Data Standards Committee"
          owningEntity: "Flight Operations"
          acronym: "AH"
          endorsedBy: "Flight Operations Committee, Maintenance Committee, Safety Committee"
          formula: "Sum of block-to-block time for all aircraft operations"
        domain: Flight Operations
      - name: Aircraft kilometres performed
        description: The sum of the products obtained by multiplying the number of revenue flight stages flown by the corresponding stage distance.
        custom_properties:
          is_confidential: 'false'
          departments: 'Airfield, Flight Operations'
          detailedDescription: 'Aircraft kilometres performed are a key metric for assessing operational efficiency and resource allocation. This data helps both airfield and flight operations teams optimize flight schedules and resource utilization.'
          type: 'metrics'
          unit: 'kilometers'
          reviewedBy: "u001:Flight Operations:Flight Operations Data Standards Committee,u005:Airfield:Airfield Data Standards Committee"
          owningEntity: "Flight Operations"
          acronym: "AKP"
          endorsedBy: "Flight Operations Committee"
          formula: "Sum of (number of revenue flight stages × stage distance)"
        domain: Flight Operations
      - name: Breakeven load factor
        description: The weight load factor at which point operating revenues equal operating expenses. It is obtained by multiplying the weight load factor by the ratio of the operating expenses to the operating revenues.
        custom_properties:
          is_confidential: 'false'
          departments: 'Airfield, Flight Operations'
          detailedDescription: 'Breakeven load factor is a critical metric for assessing operational efficiency and financial viability. This data helps both airfield and flight operations teams optimize flight schedules and resource allocation.'
          type: 'metrics'
          unit: 'percentage'
          reviewedBy: "u001:Flight Operations:Flight Operations Data Standards Committee,u005:Airfield:Airfield Data Standards Committee"
          owningEntity: "Flight Operations"
          acronym: "BLF"
          endorsedBy: "Flight Operations Committee, Finance Committee, Business Planning Committee"
          formula: "(Weight Load Factor) × (Operating Expenses / Operating Revenues) × 100%"
        domain: Flight Operations
      - name: City-pair (OFOD)
        description: Two cities between which travel is authorized by a passenger ticket or part of a ticket (a flight coupon) or between which shipments are made in accordance with a shipment document or a part of it (freight bill or mail delivery bill).
        custom_properties:
          acronym: "CP"
          owningEntity: "Flight Operations"
          synonyms: "city pair, route pair"
          lastUpdated: "2024-06-05"
          reviewedBy: "u001:Flight Operations:Flight Operations Data Standards Committee,u005:Airfield:Airfield Data Standards Committee"
          departments: 'Airfield, Flight Operations'
          type: "glossary"
        domain: Flight Operations
      - name: Code sharing
        description: The use of the flight designator code of one air carrier on a service performed by a second air carrier, which service is usually also identified (and may be required to be identified) as a service of, and being performed by, the second air carrier.
        custom_properties:
          acronym: "CS"
          owningEntity: "Flight Operations"
          synonyms: "codeshare, shared flight"
          lastUpdated: "2024-06-05"
          reviewedBy: "u002:Flight Operations:Flight Operations Data Standards Committee,u005:Airfield:Airfield Data Standards Committee"
          departments: 'Airfield, Flight Operations'
          type: "glossary"
        domain: Flight Operations
      - name: Commercial air transport operator
        description: An operator that, for remuneration, provides scheduled or non-scheduled air transport services to the public for the carriage of passengers, freight or mail. This category also includes small-scale operators, such as air taxis and commercial business operators, that provide commercial air transport services.
        custom_properties:
          acronym: "CATO"
          owningEntity: "Flight Operations"
          synonyms: "airline, operator"
          lastUpdated: "2024-06-05"
          reviewedBy: "u001:Flight Operations:Flight Operations Data Standards Committee,u005:Airfield:Airfield Data Standards Committee"
          departments: 'Airfield, Flight Operations'
          type: "glossary"
        domain: Flight Operations
      - name: Distances
        description: Aerodrome-to-aerodrome great circle distances should be used at least for international services, in all items involving distance computations. Distances can be calculated using the Great Circle Distance, defined as the shortest distance between any two points on the surface of the Earth, using the Vincenty distance formula associated with the World Geodesic System – 1984 (WGS 84) adopted by ICAO and referred to in Article ******* of Annex 15 to the Chicago Convention (WGS 84). The latitude and longitude of aerodromes can be taken either form aerodrome data published in the national Aeronautical Information Publication (AIP) or from a source using such data (for example, the Location Indicators – Doc 7910 website of ICAO).
        custom_properties:
          is_confidential: 'false'
          departments: 'Airfield, Flight Operations'
          detailedDescription: 'Accurate distance calculations are essential for both airfield operations and flight operations, impacting fuel planning, scheduling, and route optimization. These calculations form the basis for many operational and financial decisions.'
          type: 'metrics'
          unit: 'kilometers'
          reviewedBy: "u001:Flight Operations:Flight Operations Data Standards Committee,u005:Airfield:Airfield Data Standards Committee"
          owningEntity: "Flight Operations"
          acronym: "DIS"
          endorsedBy: "Flight Operations Committee"
          formula: "Great Circle Distance between aerodromes using WGS 84 coordinates"
        domain: Flight Operations
      - name: Autonomous Flight Operations
        description: Operations involving aircraft that can perform flights without human intervention, using advanced AI and machine learning algorithms to navigate and manage flight paths.
        custom_properties:
          acronym: "AFO"
          owningEntity: "Flight Operations"
          synonyms: "self-flying, pilotless"
          lastUpdated: "2024-06-05"
          reviewedBy: "u001:Flight Operations:Flight Operations Data Standards Committee,u005:Airfield:Airfield Data Standards Committee"
          departments: 'Airfield, Flight Operations'
          type: "glossary"
        domain: Flight Operations
  - name: Airports
    description: Airports
    knowledge_links:
      - label: Wiki link for classification
        url: 'https://en.wikipedia.org/wiki/Classification'
    terms:
      - name: Air taxi operations
        description: On-demand, non-scheduled flights on short notice for the carriage by air of passengers, freight or mail, or any combination thereof for remuneration usually performed with smaller aircraft including helicopters (typically no more than 30 seats). Also includes any positioning flights required for the provision of the service.
        custom_properties:
          is_confidential: 'false'
          departments: 'Airfield, Flight Operations'
          detailedDescription: 'Air taxi operations require specialized coordination between airfield and flight operations teams to handle the unique scheduling and operational requirements of on-demand air services.'
          type: 'metrics'
          unit: 'operations'
          reviewedBy: "u001:Flight Operations:Flight Operations Data Standards Committee,u005:Airfield:Airfield Data Standards Committee"
          owningEntity: "Flight Operations"
          acronym: "ATO"
          endorsedBy: "Flight Operations Committee"
          formula: "Sum of all air taxi operations within the reporting period"
        domain: Flight Operations
      - name: Aircraft movement
        description: An aircraft take-off or landing at an airport. For airport traffic purposes one arrival and one departure is counted as two movements. \n\nInternational. All flights of national or foreign aircraft whose origin or destination is located in the territory of a State other than that in which the airport being reported on is located. Domestic. All flights of national or foreign aircraft in which all the airports are located in the territory of the same State. In both cases a flight shall be considered as the operation of an aircraft on a stage or number of stages with an unchanging flight number. Technical stops are not considered.
        custom_properties:
          is_confidential: 'false'
          departments: 'Finance, Flight Operations'
          detailedDescription: 'Aircraft movements are the fundamental unit of airport operations, requiring precise coordination between airfield and flight operations teams to ensure safe and efficient handling of all arrivals and departures.'
          type: 'metrics'
          unit: 'movements'
          reviewedBy: "u001:Flight Operations:Flight Operations Data Standards Committee,u005:Airfield:Airfield Data Standards Committee"
          owningEntity: "Hamad International Airport"
          acronym: "AMV"
          endorsedBy: "Hamad International Airport"
          formula: "Count of all aircraft take-offs and landings (arrivals + departures)"
        domain: Flight Operations
      - name: PCommercial air transport
        description: Air transport services available to the general public for the transportation of passengers, mail and/or freight for remuneration. Includes air taxis and commercial business flights.
        custom_properties:
          acronym: "CAT"
          owningEntity: "Flight Operations"
          synonyms: "public air transport, commercial flights"
          lastUpdated: "2024-06-05"
          reviewedBy: "u004:Flight Operations:Flight Operations Data Standards Committee,u005:Airfield:Airfield Data Standards Committee"
          type: "glossary"
          departments: 'Flight Operations'
        domain: Flight Operations
