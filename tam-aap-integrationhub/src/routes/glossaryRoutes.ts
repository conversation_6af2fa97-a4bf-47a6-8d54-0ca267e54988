import { FastifyPluginAsync } from 'fastify';
import { GlossarySearchSchema, GlossaryTerm, ErrorResponse, GlossaryUrnSchema } from '../types/glossaryTypes.js';
import { glossarySearchController, getGlossaryTermByUrnController } from '../controllers/glossaryController.js';

const glossaryRoutes: FastifyPluginAsync = async (fastify) => {
  fastify.post('/dh-glossary', {
    schema: GlossarySearchSchema,
    handler: glossarySearchController,
  });

  fastify.get('/glossary/urn', {
    schema: GlossaryUrnSchema,
    handler: getGlossaryTermByUrnController,
  });
};

export default glossaryRoutes;