import Link from "next/link";
import Image from "next/image";
import { GlossaryItem } from "@/types/dataverse";
import { Button } from "../ui/button";

interface GlossaryTermListProps {
  items: GlossaryItem[];
  searchTerm: string;
  onViewAllClick: (letter: string) => void;
}

const highlightMatch = (text: string, query: string) => {
  if (!query.trim()) return text;

  const regex = new RegExp(
    `(${query.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")})`,
    "gi"
  );
  const parts = text.split(regex);

  return (
    <>
      {parts.map((part, i) =>
        regex.test(part) ? (
          <span key={i} className="bg-yellow-700 text-foreground rounded-md">
            {part}
          </span>
        ) : (
          <span key={i}>{part}</span>
        )
      )}
    </>
  );
};

export default function GlossaryTermList({
  items,
  searchTerm,
  onViewAllClick,
}: GlossaryTermListProps) {
  // Group items by first letter
  const groupedItems = items.reduce((acc, item) => {
    const firstLetter = item.title.charAt(0).toUpperCase();
    if (!acc[firstLetter]) {
      acc[firstLetter] = [];
    }
    acc[firstLetter].push(item);
    return acc;
  }, {} as Record<string, GlossaryItem[]>);

  // Sort letters alphabetically
  const sortedLetters = Object.keys(groupedItems).sort();

  return (
    <div className="space-y-10">
      {sortedLetters.map((letter) => (
        <div key={letter} id={`section-${letter}`} className="scroll-mt-24">
          <div className="h-16 px-6 py-4 bg-card rounded-2xl outline-2 outline-offset-[-2px] outline-gray-600 flex justify-between items-center mb-4">
            <div className="justify-center text-white text-2xl font-semibold leading-7">
              {letter}
            </div>
            <div className="flex justify-start items-center gap-2">
              <Button
                variant="ghost"
                className="justify-start text-blue-500 text-xl font-normal"
                onClick={() => onViewAllClick(letter)}
              >
                View All
              </Button>
            </div>
          </div>

          <div className="space-y-4">
            {groupedItems[letter].map((item) => (
              <div
                key={item.id}
                className="bg-card text-card-foreground rounded-lg overflow-hidden border border-border"
              >
                <div className="p-6">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2 mb-3">
                    <div className="flex flex-row gap-4">
                      <h3 className="text-3xl font-semibold leading-10">
                        {highlightMatch(item.title, searchTerm)}
                      </h3>

                      <div className="flex flex-wrap gap-2">
                        {item.domains.map((domain) => (
                          <div
                            key={`${item.id}-${domain}`}
                            className="relative w-8 h-8"
                          >
                            <Image
                              src={`/svg/${domain}.svg`}
                              alt={domain}
                              fill
                              className="object-contain"
                            />
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="flex justify-end">
                      <Link
                        href={`/dataverse/glossary/${item.id}`}
                        className="text-blue-600 dark:text-blue-400 text-xl flex gap-2"
                      >
                        <span>Learn more</span> <span> → </span>
                      </Link>
                    </div>
                  </div>

                  <p className="text-muted-foreground text-2xl mb-3">
                    {highlightMatch(item.description, searchTerm)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}
