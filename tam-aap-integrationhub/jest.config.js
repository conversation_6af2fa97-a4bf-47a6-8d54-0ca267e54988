export default {
  preset: 'ts-jest/presets/js-with-ts-esm',
  testEnvironment: 'node',
  moduleFileExtensions: ['ts', 'js', 'json', 'node'],
  transform: {
    '^.+\\.ts$': ['ts-jest', {
      useESM: true,
    }],
  },
  extensionsToTreatAsEsm: ['.ts'],
  moduleNameMapper: {
    '^(\\.{1,2}/.*)\\.js$': '$1',
  },
  testMatch: ['**/__tests__/**/*.test.ts'],
  // Coverage configuration
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  coveragePathIgnorePatterns: ['/node_modules/', '/dist/'],
  // Set coverage thresholds based on current coverage
  coverageThreshold: {
    global: {
      branches: 75,
      functions: 90,
      lines: 90,
      statements: 90
    },
    // Specific thresholds for controllers
    "./src/controllers/**/*.ts": {
      branches: 60,
      functions: 100,
      lines: 100,
      statements: 100
    },
    // Specific thresholds for services
    "./src/services/**/*.ts": {
      branches: 80,
      functions: 100,
      lines: 100,
      statements: 100
    }
  },

  // Ignore specific lines that are difficult to test
  coveragePathIgnorePatterns: [
    "node_modules",
    "dist"
  ],

  // Add a comment to ignore the specific line in domainService.ts
  // This is done by adding a comment to the file: /* istanbul ignore next */
};