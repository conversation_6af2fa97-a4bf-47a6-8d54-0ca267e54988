"use client";

import { useState } from "react";
import { Filter } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";

export type FilterOption = {
  id: string;
  label: string;
};

interface MetricsFilterProps {
  onFilterChange: (filters: {
    owningEntity: string | null;
    endorsedBy: string | null;
  }) => void;
  owningEntities: FilterOption[];
  endorsedByOptions: FilterOption[];
  className?: string;
}

export default function MetricsFilter({
  onFilterChange,
  owningEntities,
  endorsedByOptions,
  className,
}: MetricsFilterProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [owningEntity, setOwningEntity] = useState<string | null>(null);
  const [endorsedBy, setEndorsedBy] = useState<string | null>(null);

  // Don't trigger the filter change automatically on every render
  const handleOwningEntityChange = (value: string) => {
    const newValue = value === "all" ? null : value;
    setOwningEntity(newValue);
    onFilterChange({
      owningEntity: newValue,
      endorsedBy: endorsedBy,
    });
  };

  const handleEndorsedByChange = (value: string) => {
    const newValue = value === "all" ? null : value;
    setEndorsedBy(newValue);
    onFilterChange({
      owningEntity: owningEntity,
      endorsedBy: newValue,
    });
  };

  const toggleFilter = () => {
    if (isExpanded) {
      // Clear filters when closing
      setOwningEntity(null);
      setEndorsedBy(null);
      onFilterChange({
        owningEntity: null,
        endorsedBy: null,
      });
    }
    setIsExpanded(!isExpanded);
  };

  return (
    <div className={cn("w-full", className)}>
      <div className="flex justify-end mb-4">
        <Button
          variant={isExpanded ? "default" : "outline"}
          size="sm"
          onClick={toggleFilter}
          className="flex items-center gap-2"
        >
          Filters
          <Filter size={16} />
        </Button>
      </div>

      {isExpanded && (
        <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex flex-col gap-2">
            <label className="text-Grays-Gray-4 text-sm font-extralight">
              Definition Owning Entity
            </label>

            {/* <SearchableSelect
              value={owningEntity === null ? "all" : owningEntity}
              onValueChange={handleOwningEntityChange}
              //   className="!h-12 w-full px-4 !bg-gray-800 rounded-lg outline-1 outline-offset-[-1px] outline-slate-700"
              items={[
                { value: "all", label: "All" },
                ...owningEntities.map((entity) => ({
                  value: entity.id,
                  label: entity.label,
                })),
              ]}
              placeholder="Choose Entity"
              searchable={true}
              onSearch={(value) => console.log("Search query:", value)}
            /> */}
            <Select
              value={owningEntity === null ? "all" : owningEntity}
              onValueChange={handleOwningEntityChange}
            >
              <SelectTrigger className="!h-12 w-full px-4 !bg-gray-800 rounded-lg outline-1 outline-offset-[-1px] outline-slate-700">
                <SelectValue placeholder="Choose Entity" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                {owningEntities.map((entity) => (
                  <SelectItem key={entity.id} value={entity.id}>
                    {entity.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex flex-col gap-2">
            <label className="text-Grays-Gray-4 text-sm font-extralight">
              Endorsed By
            </label>
            <Select
              value={endorsedBy === null ? "all" : endorsedBy}
              onValueChange={handleEndorsedByChange}
            >
              <SelectTrigger className="!h-12 w-full px-4 !bg-gray-800 rounded-lg outline-1 outline-offset-[-1px] outline-slate-700">
                <SelectValue placeholder="Choose Entity" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                {endorsedByOptions.map((option) => (
                  <SelectItem key={option.id} value={option.id}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      )}
    </div>
  );
}
