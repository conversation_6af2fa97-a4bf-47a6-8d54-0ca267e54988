import { Type } from '@fastify/type-provider-typebox';
import { PaginationQuery } from './commonSchema';

// Common Types
export const glossaryQuerySchema = Type.Object({
  search: Type.Optional(Type.String({
    description: 'A full-text search term. Matches against both the glossary term name and description. Partial and case-insensitive matches are supported. Example: searching for "flight" will return any term or description containing "flight".',
    examples: ['Flight Operations', 'Maintenance']
  })),
  domainName: Type.Optional(Type.String({
    description: 'Restrict results to glossary terms belonging to this domain. The domain name must match exactly (case-sensitive). Example: "Flight Operations".',
    examples: ['Flight Operations']
  })),
  kind: Type.Optional(Type.String({
    enum: ['extended'],
    description: 'Set to "extended" to include additional fields in the response, such as lastUpdated, owningEntity, acronym, synonyms, reviewedBy, and unit.',
    examples: ['extended']
  })),
  exactSearchMatch: Type.Optional(Type.Boolean({
    description: 'If true, the search term will match as a substring in either the name or description (case-insensitive). If false or omitted, partial matches are allowed.',
    examples: [true, false]
  })),
  initialLetter: Type.Optional(Type.String({
    minLength: 1,
    maxLength: 1,
    description: 'Filter glossary terms whose name starts with this single letter (case-insensitive). For example, "A" will match "Aircraft departures".',
    examples: ['F', 'M']
  })),
  domain: Type.Optional(Type.String({
    description: 'Filter glossary terms by domain. This is an additional filter and can be combined with domainName.',
    examples: ['Flight Operations']
  })),
  offset: Type.Optional(Type.Integer({
    minimum: 0,
    description: 'The index of the first result to return (pagination offset). Use in combination with "limit" for paginated results. Default is 0.'
  })),
  limit: Type.Optional(Type.Integer({
    minimum: 1,
    description: 'The maximum number of results to return (pagination limit). If not provided, all results will be returned in batches of 1000 until all are fetched.'
  })),
  maxItemsPerCharacters: Type.Optional(Type.Integer({
    description: 'Maximum number of items to return per starting character of the title.',
    minimum: 1
  })),
}, {
  description: 'Query parameters for searching glossary terms in DataHub. All fields are optional and can be combined for more specific searches.'
});

// Schema used for validation (includes customProperties)
export const glossaryBodyValidationSchema = Type.Object({
  search: Type.Optional(Type.String()),
  domainName: Type.Optional(Type.String()),
  kind: Type.Optional(Type.String({ enum: ['extended'] })),
  exactSearchMatch: Type.Optional(Type.Boolean()),
  initialLetter: Type.Optional(Type.String({ minLength: 1, maxLength: 1 })),
  domain: Type.Optional(Type.String()),
  customProperties: Type.Array(
    Type.Object({
      key: Type.String({ enum: ['type'] }),
      value: Type.String({ enum: ['glossary'] })
    }),
    { default: [{ key: 'type', value: 'glossary' }] }
  )
});

// Schema used for Swagger docs
export const glossaryBodySwaggerSchema = Type.Object({
  search: Type.Optional(Type.String({
    description: 'A full-text search term. Matches against both the glossary term name and description. Partial and case-insensitive matches are supported. Example: searching for "flight" will return any term or description containing "flight".',
    examples: ['Flight Operations', 'Maintenance']
  })),
  domainName: Type.Optional(Type.String({
    description: 'Restrict results to glossary terms belonging to this domain. The domain name must match exactly (case-sensitive). Example: "Flight Operations".',
    examples: ['Flight Operations']
  })),
  kind: Type.Optional(Type.String({
    enum: ['extended'],
    description: 'Set to "extended" to include additional fields in the response, such as lastUpdated, owningEntity, acronym, synonyms, reviewedBy, and unit.',
    examples: ['extended']
  })),
  exactSearchMatch: Type.Optional(Type.Boolean({
    description: 'If true, the search term will match as a substring in either the name or description (case-insensitive). If false or omitted, partial matches are allowed.',
    examples: [true, false]
  })),
  initialLetter: Type.Optional(Type.String({
    minLength: 1,
    maxLength: 1,
    description: 'Filter glossary terms whose name starts with this single letter (case-insensitive). For example, "A" will match "Aircraft departures".',
    examples: ['F', 'M']
  })),
  domain: Type.Optional(Type.String({
    description: 'Filter glossary terms by domain. This is an additional filter and can be combined with domainName.',
    examples: ['Flight Operations']
  }))
}, {
  description: 'Request body for searching glossary terms. All fields are optional and can be combined for more specific searches.'
  // To add request examples for Swagger, use the route schema (glossaryRouteSchema) with the x-examples OpenAPI extension.
});

// Define the base glossary item schema
const glossaryItemBaseSchema = Type.Object({
  id: Type.String(),
  title: Type.String(),
  description: Type.String(),
  domains: Type.Array(Type.String(), { default: [] }),
  unit: Type.Optional(Type.String()),
});

// Define the reviewedBy schema
const reviewedBySchema = Type.Object({
  id: Type.String(),
  department: Type.String(),
  name: Type.String()
});

// Define the extended glossary item schema with additional fields
const glossaryItemExtendedSchema = Type.Intersect([
  Type.Object({
    id: Type.String(),
    title: Type.String(),
    description: Type.String(),
    domains: Type.Array(Type.String(), { default: [] }),
    unit: Type.Optional(Type.String()),
  }),
  Type.Object({
    lastUpdated: Type.Optional(Type.String()),
    owningEntity: Type.Optional(Type.String()),
    acronym: Type.Optional(Type.String()),
    synonyms: Type.Optional(Type.Array(Type.String())),
    reviewedBy: Type.Optional(Type.Array(reviewedBySchema))
  })
]);

// Define the response schema that can handle both basic and extended items
export const glossaryResponseSchema = Type.Object({
  offset: Type.Integer(),
  limit: Type.Integer(),
  total: Type.Integer(),
  results: Type.Array(glossaryItemExtendedSchema)
});

export const glossaryRouteSchema = {
  tags: ['Glossary'],
  summary: 'Search airport glossary terms',
  description: 'Search for airport glossary terms with flexible filtering options including domain, department, and more.',
  querystring: glossaryQuerySchema,
  response: {
    200: glossaryResponseSchema
  },
  'x-examples': {
    'Search glossary': {
      value: {
        search: 'Flight Operations',
        domainName: 'Flight Operations',
        kind: 'extended',
        exactSearchMatch: true,
        initialLetter: 'F',
        domain: 'Airfield'
      }
    }
  }
};

export const glossaryByIdRouteSchema = {
  tags: ['Glossary'],
  summary: 'Get glossary term by ID',
  description: 'Fetch a single glossary term by its ID.',
  params: Type.Object({
    id: Type.String({ description: 'The ID of the glossary term to fetch.' })
  }),
  response: {
    200: glossaryItemExtendedSchema,
    404: Type.Object({
      error: Type.String()
    }),
    502: Type.Object({
      error: Type.String()
    })
  }
};