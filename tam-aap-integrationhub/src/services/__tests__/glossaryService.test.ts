import { searchGlossaryTerms, getGlossaryTermByUrn } from '../glossaryService.js';
import { FastifyRequest } from 'fastify';
import { GlossarySearchData, GlossaryTermResponse } from '../../types/glossaryTypes.js';

describe('glossaryService', () => {
  describe('searchGlossaryTerms', () => {
    it('should return search results in the expected structure', async () => {
      const mockSearchResults = [
        {
          entity: {
            urn: 'urn:li:glossaryTerm:1',
            name: 'Original Name',
            glossaryTermInfo: {
              name: 'Transformed Name',
              description: 'A description',
              customProperties: [
                { key: 'type', value: 'metrics' }
              ]
            }
          }
        }
      ];
      const mockResponse: { data: GlossarySearchData } = {
        data: {
          search: {
            start: 0,
            count: 1,
            total: 1,
            searchResults: mockSearchResults
          }
        }
      };
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue(mockResponse)
        }
      } as unknown as FastifyRequest;

      const filters = { search: 'Transformed Name', start: 0, count: 1 };
      const result = await searchGlossaryTerms(mockRequest, filters);
      expect(result).toEqual(mockResponse);
      expect(mockRequest.datahub.query).toHaveBeenCalled();
    });

    it('should throw if DataHub returns errors', async () => {
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue({ errors: [{ message: 'GraphQL error' }] })
        }
      } as unknown as FastifyRequest;
      await expect(searchGlossaryTerms(mockRequest, { search: 'foo', start: 0, count: 1 }))
        .rejects.toThrow('GraphQL query failed: searching glossary terms with query "foo"');
    });

    it('should throw if domainName is provided but not found', async () => {
      const mockRequest = {
        datahub: {
          query: jest.fn()
            // First call: domain search returns no results
            .mockResolvedValueOnce({ data: { search: { searchResults: [] } } })
        }
      } as unknown as FastifyRequest;
      await expect(searchGlossaryTerms(mockRequest, { domainName: 'Nonexistent Domain' }))
        .rejects.toThrow("Domain with name 'Nonexistent Domain' not found");
    });

    it('should include domain URN in filters if domainName is provided', async () => {
      const mockDomainUrn = 'urn:li:domain:123';
      const mockRequest = {
        datahub: {
          query: jest.fn()
            // First call: domain search
            .mockResolvedValueOnce({ data: { search: { searchResults: [ { entity: { urn: mockDomainUrn } } ] } } })
            // Second call: glossary search
            .mockResolvedValueOnce({ data: { search: { start: 0, count: 1, total: 1, searchResults: [] } } })
        }
      } as unknown as FastifyRequest;
      const filters = { domainName: 'Flight Operations', search: 'foo', start: 0, count: 1 };
      const result = await searchGlossaryTerms(mockRequest, filters);
      expect(result.data.search.searchResults).toEqual([]);
      expect(mockRequest.datahub.query).toHaveBeenCalledTimes(2);
      // The first call is for domain URN, the second is for glossary search
      const queryMock = mockRequest.datahub.query as jest.Mock;
      expect(queryMock.mock.calls[0][0]).toContain('type: DOMAIN');
      expect(queryMock.mock.calls[1][0]).toContain('type: GLOSSARY_TERM');
    });

    it('should fetch all results if count is not provided', async () => {
      const mockSearchResults = [
        { entity: { urn: 'urn:li:glossaryTerm:1', name: 'A', glossaryTermInfo: { name: 'A', description: '', customProperties: [] } } },
        { entity: { urn: 'urn:li:glossaryTerm:2', name: 'B', glossaryTermInfo: { name: 'B', description: '', customProperties: [] } } }
      ];
      // Simulate two pages: first page returns 1, second page returns 1, then done
      const mockRequest = {
        datahub: {
          query: jest.fn()
            .mockResolvedValueOnce({ data: { search: { start: 0, count: 1, total: 2, searchResults: [mockSearchResults[0]] } } })
            .mockResolvedValueOnce({ data: { search: { start: 1, count: 1, total: 2, searchResults: [mockSearchResults[1]] } } })
        }
      } as unknown as FastifyRequest;
      const filters = { search: 'foo' };
      const result = await searchGlossaryTerms(mockRequest, filters);
      expect(result.data.search.searchResults.length).toBe(2);
      expect(result.data.search.searchResults[0]).toEqual(mockSearchResults[0]);
      expect(result.data.search.searchResults[1]).toEqual(mockSearchResults[1]);
    });

    it('should use wildcard query if filters.search is missing', async () => {
      const mockResponse = {
        data: {
          search: {
            start: 0,
            count: 1,
            total: 1,
            searchResults: []
          }
        }
      };
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue(mockResponse)
        }
      } as unknown as FastifyRequest;
      const filters = { start: 0, count: 1 };
      await searchGlossaryTerms(mockRequest, filters);
      // The query should use '*'
      const queryMock1 = mockRequest.datahub.query as jest.Mock;
      expect(queryMock1.mock.calls[0][0]).toContain('query: "*"');
    });

    it('should default start to 0 if filters.start is missing', async () => {
      const mockResponse = {
        data: {
          search: {
            start: 0,
            count: 1,
            total: 1,
            searchResults: []
          }
        }
      };
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue(mockResponse)
        }
      } as unknown as FastifyRequest;
      const filters = { search: 'foo', count: 1 };
      await searchGlossaryTerms(mockRequest, filters);
      // The query should use start: 0
      const queryMock2 = mockRequest.datahub.query as jest.Mock;
      expect(queryMock2.mock.calls[0][0]).toContain('start: 0');
    });

    it('should default count to 1000 if filters.count is missing', async () => {
      const mockResponse = {
        data: {
          search: {
            start: 0,
            count: 1000,
            total: 1,
            searchResults: []
          }
        }
      };
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue(mockResponse)
        }
      } as unknown as FastifyRequest;
      const filters = { search: 'foo', start: 0 };
      await searchGlossaryTerms(mockRequest, filters);
      // The query should use count: 1000
      const queryMock3 = mockRequest.datahub.query as jest.Mock;
      expect(queryMock3.mock.calls[0][0]).toContain('count: 1000');
    });

    it('should return empty array if searchResults is undefined', async () => {
      const mockResponse = {
        data: {
          search: {
            start: 0,
            count: 1,
            total: 1,
            searchResults: undefined
          }
        }
      };
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue(mockResponse)
        }
      } as unknown as FastifyRequest;
      const filters = { search: 'foo', start: 0, count: 1 };
      const result = await searchGlossaryTerms(mockRequest, filters);
      expect(result.data.search.searchResults).toEqual([]);
    });

    it('should return empty results if fetchAllGlossaryResults returns empty array', async () => {
      // Patch fetchAllGlossaryResults to return []
      const original = jest.requireActual('../glossaryService.js');
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue({
            data: { search: { start: 0, count: 0, total: 0, searchResults: [] } }
          })
        }
      } as unknown as FastifyRequest;
      // Simulate count not being a number
      const filters = { search: 'foo' };
      const result = await original.searchGlossaryTerms(mockRequest, filters);
      expect(result.data.search.searchResults).toEqual([]);
      expect(result.data.search.count).toBe(0);
      expect(result.data.search.total).toBe(0);
    });

    it('should break pagination when allResults.length >= total (not just searchResults.length === 0)', async () => {
      // Simulate one page: returns 2 results, total is 2, so allResults.length >= total
      const mockRequest = {
        datahub: {
          query: jest.fn()
            .mockResolvedValueOnce({ data: { search: { start: 0, count: 2, total: 2, searchResults: [
              { entity: { urn: 'urn:li:glossaryTerm:1', name: 'A', glossaryTermInfo: { name: 'A', description: '', customProperties: [] } } },
              { entity: { urn: 'urn:li:glossaryTerm:2', name: 'B', glossaryTermInfo: { name: 'B', description: '', customProperties: [] } } }
            ] } } })
        }
      } as unknown as FastifyRequest;
      // Simulate count not being a number
      const filters = { search: 'foo' };
      const original = jest.requireActual('../glossaryService.js');
      const result = await original.searchGlossaryTerms(mockRequest, filters);
      expect(result.data.search.searchResults.length).toBe(2);
      expect(result.data.search.searchResults[0].entity.urn).toBe('urn:li:glossaryTerm:1');
      expect(result.data.search.searchResults[1].entity.urn).toBe('urn:li:glossaryTerm:2');
    });

    it('should skip domain URN logic if domainName is an empty string', async () => {
      const mockResponse = {
        data: {
          search: {
            start: 0,
            count: 1,
            total: 1,
            searchResults: [
              {
                entity: {
                  urn: 'urn:li:glossaryTerm:1',
                  name: 'A',
                  glossaryTermInfo: { name: 'A', description: '', customProperties: [] }
                }
              }
            ]
          }
        }
      };
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue(mockResponse)
        }
      } as unknown as FastifyRequest;
      const filters = { domainName: '', search: 'A', start: 0, count: 1 };
      const result = await searchGlossaryTerms(mockRequest, filters);
      expect(result.data.search.searchResults.length).toBe(1);
      expect(mockRequest.datahub.query).toHaveBeenCalledTimes(1);
    });

    it('should return empty array if searchResults is an empty array', async () => {
      const mockResponse = {
        data: {
          search: {
            start: 0,
            count: 1,
            total: 1,
            searchResults: []
          }
        }
      };
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue(mockResponse)
        }
      } as unknown as FastifyRequest;
      const filters = { search: 'foo', start: 0, count: 1 };
      const result = await searchGlossaryTerms(mockRequest, filters);
      expect(result.data.search.searchResults).toEqual([]);
    });

    it('should skip domain URN logic if domainName is not provided', async () => {
      const mockResponse = {
        data: {
          search: {
            start: 0,
            count: 1,
            total: 1,
            searchResults: [
              {
                entity: {
                  urn: 'urn:li:glossaryTerm:2',
                  name: 'B',
                  glossaryTermInfo: { name: 'B', description: '', customProperties: [] }
                }
              }
            ]
          }
        }
      };
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue(mockResponse)
        }
      } as unknown as FastifyRequest;
      const filters = { search: 'B', start: 0, count: 1 };
      const result = await searchGlossaryTerms(mockRequest, filters);
      expect(result.data.search.searchResults.length).toBe(1);
      expect(result.data.search.searchResults[0].entity.urn).toBe('urn:li:glossaryTerm:2');
      expect(mockRequest.datahub.query).toHaveBeenCalledTimes(1);
    });

    it('should return non-empty array if searchResults is a non-empty array', async () => {
      const mockResponse = {
        data: {
          search: {
            start: 0,
            count: 1,
            total: 1,
            searchResults: [
              {
                entity: {
                  urn: 'urn:li:glossaryTerm:3',
                  name: 'C',
                  glossaryTermInfo: { name: 'C', description: '', customProperties: [] }
                }
              }
            ]
          }
        }
      };
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue(mockResponse)
        }
      } as unknown as FastifyRequest;
      const filters = { search: 'C', start: 0, count: 1 };
      const result = await searchGlossaryTerms(mockRequest, filters);
      expect(result.data.search.searchResults).toHaveLength(1);
      expect(result.data.search.searchResults[0].entity.urn).toBe('urn:li:glossaryTerm:3');
    });

    it('should use wildcard query if filters.search is not provided', async () => {
      const mockResponse = {
        data: {
          search: {
            start: 0,
            count: 1,
            total: 1,
            searchResults: [
              {
                entity: {
                  urn: 'urn:li:glossaryTerm:5',
                  name: 'E',
                  glossaryTermInfo: { name: 'E', description: '', customProperties: [] }
                }
              }
            ]
          }
        }
      };
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue(mockResponse)
        }
      } as unknown as FastifyRequest;
      const filters = { start: 0, count: 1 };
      await searchGlossaryTerms(mockRequest, filters);
      const queryMock = mockRequest.datahub.query as jest.Mock;
      expect(queryMock.mock.calls[0][0]).toContain('query: "*"');
    });
  });

  describe('getGlossaryTermByUrn', () => {
    it('should return a glossary term entity for a given URN', async () => {
      const mockGlossaryTerm = {
        urn: 'urn:li:glossaryTerm:1',
        name: 'Original Name',
        glossaryTermInfo: {
          name: 'Transformed Name',
          description: 'A description',
          customProperties: [
            { key: 'type', value: 'metrics' }
          ]
        }
      };
      const mockResponse = {
        data: {
          glossaryTerm: mockGlossaryTerm
        }
      };
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue(mockResponse)
        }
      } as unknown as FastifyRequest;

      const urn = 'urn:li:glossaryTerm:1';
      const result = await getGlossaryTermByUrn(mockRequest, urn);
      expect(result).toEqual(mockGlossaryTerm);
      expect(mockRequest.datahub.query).toHaveBeenCalled();
    });

    it('should throw if DataHub returns errors', async () => {
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue({ errors: [{ message: 'GraphQL error' }] })
        }
      } as unknown as FastifyRequest;
      await expect(getGlossaryTermByUrn(mockRequest, 'urn:li:glossaryTerm:1'))
        .rejects.toThrow('GraphQL query failed: fetching glossary term with URN "urn:li:glossaryTerm:1"');
    });
  });
}); 