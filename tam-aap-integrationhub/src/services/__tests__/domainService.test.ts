import { FastifyRequest } from 'fastify';
import { searchDomains, getDomainByUrn } from '../domainService.js';
import { buildDomainSearchQuery, buildGetDomainByUrnQuery } from '../../utils/domainQueryBuilder.js';
import { DomainSearchData, DomainApiResponse } from '../../types/domainTypes.js';
import { GraphQLResponse } from '../../types/fastifyTypes.js';

// Mock dependencies
jest.mock('../../utils/domainQueryBuilder.js');

// Mock implementations
const mockBuildDomainSearchQuery = buildDomainSearchQuery as jest.MockedFunction<typeof buildDomainSearchQuery>;
const mockBuildGetDomainByUrnQuery = buildGetDomainByUrnQuery as jest.MockedFunction<typeof buildGetDomainByUrnQuery>;

describe('domainService', () => {
  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
    mockBuildDomainSearchQuery.mockReturnValue('mock-search-query');
    mockBuildGetDomainByUrnQuery.mockReturnValue('mock-domain-query');
  });

  // We'll test the fetchAllDomainResults function indirectly through searchDomains

  describe('searchDomains', () => {
    it('should fetch all domains when count is not provided', async () => {
      // Mock data for first page
      const mockFirstPageResults = [
        {
          entity: {
            urn: 'urn:li:domain:1',
            properties: {
              name: 'Test Domain 1',
              description: 'Description 1'
            }
          }
        },
        {
          entity: {
            urn: 'urn:li:domain:2',
            properties: {
              name: 'Test Domain 2',
              description: 'Description 2'
            }
          }
        }
      ];

      // Mock data for second page
      const mockSecondPageResults = [
        {
          entity: {
            urn: 'urn:li:domain:3',
            properties: {
              name: 'Test Domain 3',
              description: 'Description 3'
            }
          }
        }
      ];

      // Mock responses for each page
      const mockFirstPageResponse: GraphQLResponse<DomainSearchData> = {
        data: {
          search: {
            start: 0,
            count: 2,
            total: 3, // Total is more than the results in first page
            searchResults: mockFirstPageResults
          }
        }
      };

      const mockSecondPageResponse: GraphQLResponse<DomainSearchData> = {
        data: {
          search: {
            start: 2,
            count: 1,
            total: 3,
            searchResults: mockSecondPageResults
          }
        }
      };

      // Mock request with DataHub client
      const mockRequest = {
        datahub: {
          query: jest.fn()
            .mockResolvedValueOnce(mockFirstPageResponse)
            .mockResolvedValueOnce(mockSecondPageResponse)
        }
      } as unknown as FastifyRequest;

      // Mock the query builder to return different queries for different pages
      mockBuildDomainSearchQuery
        .mockReturnValueOnce('mock-search-query-page-1')
        .mockReturnValueOnce('mock-search-query-page-2');

      // Call the service
      const result = await searchDomains(mockRequest, { search: 'test' });

      // Assertions
      expect(mockBuildDomainSearchQuery).toHaveBeenCalledTimes(2);
      expect(mockBuildDomainSearchQuery).toHaveBeenNthCalledWith(1, 'test', 0, 1000);
      expect(mockBuildDomainSearchQuery).toHaveBeenNthCalledWith(2, 'test', 1000, 1000);

      expect(mockRequest.datahub.query).toHaveBeenCalledTimes(2);
      expect(mockRequest.datahub.query).toHaveBeenNthCalledWith(1, 'mock-search-query-page-1');
      expect(mockRequest.datahub.query).toHaveBeenNthCalledWith(2, 'mock-search-query-page-2');

      // The result should contain all domains from both pages
      expect(result).toEqual({
        data: {
          search: {
            start: 0,
            count: 3,
            total: 3,
            searchResults: [...mockFirstPageResults, ...mockSecondPageResults]
          }
        }
      });
    });

    it('should use pagination when count is provided', async () => {
      // Mock data
      const mockSearchResults = [
        {
          entity: {
            urn: 'urn:li:domain:1',
            properties: {
              name: 'Test Domain 1',
              description: 'Description 1'
            }
          }
        }
      ];

      const mockResponse: GraphQLResponse<DomainSearchData> = {
        data: {
          search: {
            start: 0,
            count: 10,
            total: 1,
            searchResults: mockSearchResults
          }
        }
      };

      // Mock request with DataHub client
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue(mockResponse)
        }
      } as unknown as FastifyRequest;

      // Call the service
      const result = await searchDomains(mockRequest, { search: 'test', start: 0, count: 10 });

      // Assertions
      expect(mockBuildDomainSearchQuery).toHaveBeenCalledWith('test', 0, 10);
      expect(mockRequest.datahub.query).toHaveBeenCalledWith('mock-search-query');
      expect(result).toEqual({
        data: {
          search: {
            start: 0,
            count: 10,
            total: 1,
            searchResults: mockSearchResults
          }
        }
      });
    });

    it('should handle undefined searchResults in pagination', async () => {
      // Mock data with undefined searchResults
      // We need to use a type assertion to test the undefined case
      const mockResponse = {
        data: {
          search: {
            start: 0,
            count: 10,
            total: 0,
            searchResults: null // Use null instead of undefined for testing
          }
        }
      } as unknown as GraphQLResponse<DomainSearchData>;

      // Mock request with DataHub client
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue(mockResponse)
        }
      } as unknown as FastifyRequest;

      // Call the service
      const result = await searchDomains(mockRequest, { search: 'test', start: 0, count: 10 });

      // Assertions
      expect(mockBuildDomainSearchQuery).toHaveBeenCalledWith('test', 0, 10);
      expect(mockRequest.datahub.query).toHaveBeenCalledWith('mock-search-query');
      expect(result).toEqual({
        data: {
          search: {
            start: 0,
            count: 10,
            total: 0,
            searchResults: [] // Should default to empty array
          }
        }
      });
    });

    it('should use default values for start and count when not provided', async () => {
      // Mock data
      const mockSearchResults = [
        {
          entity: {
            urn: 'urn:li:domain:1',
            properties: {
              name: 'Test Domain 1',
              description: 'Description 1'
            }
          }
        }
      ];

      const mockResponse: GraphQLResponse<DomainSearchData> = {
        data: {
          search: {
            start: 0,
            count: 1000,
            total: 1,
            searchResults: mockSearchResults
          }
        }
      };

      // Mock request with DataHub client
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue(mockResponse)
        }
      } as unknown as FastifyRequest;

      // Call the service with only count (no start)
      const result = await searchDomains(mockRequest, { search: 'test', count: 1000 });

      // Assertions
      expect(mockBuildDomainSearchQuery).toHaveBeenCalledWith('test', 0, 1000); // Default start is 0
      expect(mockRequest.datahub.query).toHaveBeenCalledWith('mock-search-query');
      expect(result).toEqual({
        data: {
          search: {
            start: 0,
            count: 1000,
            total: 1,
            searchResults: mockSearchResults
          }
        }
      });
    });

    it('should use default count value when count is not a number', async () => {
      // Mock data
      const mockSearchResults = [
        {
          entity: {
            urn: 'urn:li:domain:1',
            properties: {
              name: 'Test Domain 1',
              description: 'Description 1'
            }
          }
        }
      ];

      const mockResponse: GraphQLResponse<DomainSearchData> = {
        data: {
          search: {
            start: 0,
            count: 1000, // Default count value
            total: 1,
            searchResults: mockSearchResults
          }
        }
      };

      // Mock request with DataHub client
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue(mockResponse)
        }
      } as unknown as FastifyRequest;

      // Spy on buildDomainSearchQuery to verify the arguments
      const spy = jest.spyOn(require('../../utils/domainQueryBuilder.js'), 'buildDomainSearchQuery');

      // Call the service with count as a string (not a number)
      // This should trigger the default value branch
      await searchDomains(mockRequest, {
        search: 'test',
        start: 0,
        count: 'not-a-number' as any // Force a non-number value
      });

      // Verify that buildDomainSearchQuery was called with the default count value
      expect(spy).toHaveBeenCalledWith('test', 0, 1000);

      // Restore the original implementation
      spy.mockRestore();
    });

    it('should use default count value when count is undefined', async () => {
      // Mock data
      const mockSearchResults = [
        {
          entity: {
            urn: 'urn:li:domain:1',
            properties: {
              name: 'Test Domain 1',
              description: 'Description 1'
            }
          }
        }
      ];

      const mockResponse: GraphQLResponse<DomainSearchData> = {
        data: {
          search: {
            start: 0,
            count: 1000, // Default count value
            total: 1,
            searchResults: mockSearchResults
          }
        }
      };

      // Mock request with DataHub client
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue(mockResponse)
        }
      } as unknown as FastifyRequest;

      // Spy on buildDomainSearchQuery to verify the arguments
      const spy = jest.spyOn(require('../../utils/domainQueryBuilder.js'), 'buildDomainSearchQuery');

      // Call the service with undefined count
      // This should trigger the default value branch
      await searchDomains(mockRequest, {
        search: 'test',
        start: 0,
        // count is intentionally omitted
      });

      // Verify that buildDomainSearchQuery was called with the default count value
      expect(spy).toHaveBeenCalledWith('test', 0, 1000);

      // Restore the original implementation
      spy.mockRestore();
    });

    it('should handle null searchResults in fetchAllDomainResults', async () => {
      // Mock data with null searchResults
      const mockResponse = {
        data: {
          search: {
            start: 0,
            count: 1000,
            total: 0,
            searchResults: null // Use null to test the || [] branch
          }
        }
      } as unknown as GraphQLResponse<DomainSearchData>;

      // Mock request with DataHub client
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue(mockResponse)
        }
      } as unknown as FastifyRequest;

      // Call the service
      const result = await searchDomains(mockRequest, { search: 'test' });

      // Assertions
      expect(mockBuildDomainSearchQuery).toHaveBeenCalledWith('test', 0, 1000);
      expect(mockRequest.datahub.query).toHaveBeenCalledWith('mock-search-query');
      expect(result).toEqual({
        data: {
          search: {
            start: 0,
            count: 0,
            total: 0,
            searchResults: []
          }
        }
      });
    });

    it('should use wildcard query when search is not provided', async () => {
      // Mock data
      const mockResponse: GraphQLResponse<DomainSearchData> = {
        data: {
          search: {
            start: 0,
            count: 0,
            total: 0,
            searchResults: []
          }
        }
      };

      // Mock request with DataHub client
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue(mockResponse)
        }
      } as unknown as FastifyRequest;

      // Call the service
      await searchDomains(mockRequest, { start: 0, count: 10 });

      // Assertions
      expect(mockBuildDomainSearchQuery).toHaveBeenCalledWith('*', 0, 10);
    });

    it('should handle GraphQL errors', async () => {
      // Mock data
      const mockResponse: GraphQLResponse<DomainSearchData> = {
        data: {
          search: {
            start: 0,
            count: 0,
            total: 0,
            searchResults: []
          }
        },
        errors: [{ message: 'GraphQL error' }]
      };

      // Mock request with DataHub client
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue(mockResponse)
        }
      } as unknown as FastifyRequest;

      // Call the service and expect it to throw
      await expect(searchDomains(mockRequest, { search: 'test', count: 10 }))
        .rejects.toThrow('GraphQL query failed: searching domains with query "test"');
    });
  });

  describe('getDomainByUrn', () => {
    it('should fetch a domain by URN', async () => {
      // Mock data
      const mockDomain = {
        urn: 'urn:li:domain:1',
        properties: {
          name: 'Test Domain',
          description: 'Test Description'
        }
      };

      const mockResponse: GraphQLResponse<DomainApiResponse> = {
        data: {
          domain: mockDomain
        }
      };

      // Mock request with DataHub client
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue(mockResponse)
        }
      } as unknown as FastifyRequest;

      // Call the service
      const result = await getDomainByUrn(mockRequest, 'urn:li:domain:1');

      // Assertions
      expect(mockBuildGetDomainByUrnQuery).toHaveBeenCalledWith('urn:li:domain:1');
      expect(mockRequest.datahub.query).toHaveBeenCalledWith('mock-domain-query');
      expect(result).toEqual(mockDomain);
    });

    it('should handle GraphQL errors', async () => {
      // Mock data
      const mockResponse: GraphQLResponse<DomainApiResponse> = {
        data: {
          domain: {
            urn: '',
            properties: undefined
          }
        },
        errors: [{ message: 'GraphQL error' }]
      };

      // Mock request with DataHub client
      const mockRequest = {
        datahub: {
          query: jest.fn().mockResolvedValue(mockResponse)
        }
      } as unknown as FastifyRequest;

      // Call the service and expect it to throw
      await expect(getDomainByUrn(mockRequest, 'urn:li:domain:1'))
        .rejects.toThrow('GraphQL query failed: fetching domain with URN "urn:li:domain:1"');
    });
  });
});
