"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";
import { Menu } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  SheetTrigger,
  SheetClose,
} from "@/components/ui/sheet";

import Logo from "../icons/Logo";
import ExpandableSearch from "./expandable-search";
import { useSession, signIn, signOut } from "next-auth/react";

const dataverseComponents = [
  { title: "Business Glossary", href: "/dataverse//glossary" },
  { title: "Airport Metrics", href: "/dataverse//metrics" },
  { title: "Airport Reports", href: "/dataverse//reports" },
  { title: "Data Model", href: "/dataverse/model" },
  { title: "Data Dictionary", href: "/dataverse/dictionary" },
  { title: "Data Quality Rules", href: "/dataverse/quality" },
  { title: "Data Contracts", href: "/dataverse/contracts" },
  { title: "Reference Datasets", href: "/dataverse/datasets" },
];

export default function Header() {
  const [dataverseOpen, setDataverseOpen] = useState(false);

  return (
    <header className="sticky top-0 z-50 bg-background">
      <div className="px-4 md:px-15 py-5 border-b border-gray-200 dark:border-gray-800">
        <div className="flex items-center justify-between">
          <div className="flex flex-col items-start">
            <Link href="/" className="mb-1">
              <Logo className="text-black dark:text-white" />
            </Link>
            <span className="text-xs text-muted-foreground md:text-sm">
              Powered by EMMA Systems
            </span>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex space-x-10 items-center">
            <NavigationMenu>
              <NavigationMenuList>
                <NavigationMenuItem>
                  <NavigationMenuTrigger>Dataverse</NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <ul className="grid w-[180px] gap-2 p-2">
                      {dataverseComponents.map((component) => (
                        <ListItem key={component.title} href={component.href}>
                          {component.title}
                        </ListItem>
                      ))}
                    </ul>
                  </NavigationMenuContent>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <Link href="/tam" legacyBehavior passHref>
                    <NavigationMenuLink
                      className={navigationMenuTriggerStyle()}
                    >
                      TAM
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <Link href="/airport-acument-academy" legacyBehavior passHref>
                    <NavigationMenuLink
                      className={navigationMenuTriggerStyle()}
                    >
                      Airport Acumen Academy
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <Link href="/airport-community" legacyBehavior passHref>
                    <NavigationMenuLink
                      className={navigationMenuTriggerStyle()}
                    >
                      Airport Community
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <Link href="/technical-resources" legacyBehavior passHref>
                    <NavigationMenuLink
                      className={navigationMenuTriggerStyle()}
                    >
                      Technical Resources
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>

            <div className="flex space-x-2">
              <AuthButton />
              <ExpandableSearch />
            </div>
          </div>

          {/* Mobile Navigation */}
          <div className="flex md:hidden space-x-2 items-center">
            <ExpandableSearch />
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Menu className="h-6 w-6" />
                  <span className="sr-only">Toggle menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[80%] sm:w-[350px]">
                <div className="flex flex-col h-full py-6">
                  <div className="space-y-4 flex-1">
                    <div className="px-2">
                      <AuthButton />
                    </div>

                    <div className="py-2">
                      <div
                        className="flex items-center justify-between px-2 py-3 font-medium"
                        onClick={() => setDataverseOpen(!dataverseOpen)}
                      >
                        <span>Dataverse</span>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className={`h-4 w-4 transition-transform ${
                            dataverseOpen ? "rotate-180" : ""
                          }`}
                        >
                          <polyline points="6 9 12 15 18 9"></polyline>
                        </svg>
                      </div>
                      {dataverseOpen && (
                        <div className="pl-4 mt-1 border-l-2 border-muted ml-2">
                          {dataverseComponents.map((component) => (
                            <SheetClose asChild key={component.title}>
                              <Link
                                href={component.href}
                                className="block px-2 py-2 text-sm hover:bg-accent rounded-md"
                              >
                                {component.title}
                              </Link>
                            </SheetClose>
                          ))}
                        </div>
                      )}
                    </div>

                    <SheetClose asChild>
                      <Link
                        href="/tam"
                        className="flex items-center px-2 py-3 font-medium hover:bg-accent rounded-md"
                      >
                        TAM
                      </Link>
                    </SheetClose>

                    <SheetClose asChild>
                      <Link
                        href="/airport-acument-academy"
                        className="flex items-center px-2 py-3 font-medium hover:bg-accent rounded-md"
                      >
                        Airport Acumen Academy
                      </Link>
                    </SheetClose>

                    <SheetClose asChild>
                      <Link
                        href="/airport-community"
                        className="flex items-center px-2 py-3 font-medium hover:bg-accent rounded-md"
                      >
                        Airport Community
                      </Link>
                    </SheetClose>

                    <SheetClose asChild>
                      <Link
                        href="/technical-resources"
                        className="flex items-center px-2 py-3 font-medium hover:bg-accent rounded-md"
                      >
                        Technical Resources
                      </Link>
                    </SheetClose>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
}

function AuthButton() {
  const { data: session, status } = useSession();

  if (status === "authenticated") {
    console.log("Authenticated session", session);
    return (
      <Button
        variant="primary"
        onClick={() =>
          keyCloackSessionLoutout().then((success) => {
            if (success || true) {
              // Fallback to NextAuth logout even if Keycloak logout fails
              signOut({ callbackUrl: "/" });
            }
          })
        }
      >
        Log out: {session.user?.name}
      </Button>
    );
  }

  return (
    <Button variant="primary" onClick={() => signIn("keycloak")}>
      Log in
    </Button>
  );
}

async function keyCloackSessionLoutout() {
  try {
    const response = await fetch(`/api/auth/logout`, { method: "GET" });
    if (!response.ok) {
      console.error("Keycloak logout failed:", response.status);
    }
    return response.ok;
  } catch (error) {
    console.error("Error logging out from Keycloak:", error);
    return false;
  }
}

const ListItem = React.forwardRef<
  HTMLAnchorElement,
  React.ComponentPropsWithoutRef<"a"> & { external?: boolean }
>(({ className, children, external, ...props }, ref) => {
  return (
    <li>
      <NavigationMenuLink asChild>
        <a
          ref={ref}
          target={external ? "_blank" : undefined}
          rel={external ? "noopener noreferrer" : undefined}
          className={cn(
            "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
            className
          )}
          {...props}
        >
          <div className="text-sm font-medium leading-none">{children}</div>
        </a>
      </NavigationMenuLink>
    </li>
  );
});

ListItem.displayName = "ListItem";
