import { FastifyPluginAsync } from 'fastify';
import { HealthSchema, RootSchema } from '../types/healthTypes.js';

const healthRoutes: FastifyPluginAsync = async (fastify) => {
  fastify.get('/health', {
    schema: HealthSchema
  }, async (request, reply) => {
    return { status: 'ok' };
  });

  fastify.get('/', {
    schema: RootSchema
  }, async (request, reply) => {
    return { message: 'Welcome to the Integration Hub!' };
  });
};

export default healthRoutes; 