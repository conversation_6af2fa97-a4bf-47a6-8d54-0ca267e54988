import * as config from '../config';

describe('config', () => {
  afterEach(() => {
    jest.resetModules();
    delete process.env.INTEGRATION_HUB_PORT;
    delete process.env.INTEGRATION_HUB_HOST;
    delete process.env.DATAHUB_PORT;
    delete process.env.DATAHUB_HOST;
    delete process.env.DATAHUB_TOKEN;
  });

  it('uses default values when env vars are not set', () => {
    delete process.env.INTEGRATION_HUB_PORT;
    delete process.env.INTEGRATION_HUB_HOST;
    delete process.env.DATAHUB_PORT;
    delete process.env.DATAHUB_HOST;
    delete process.env.DATAHUB_TOKEN;

    jest.resetModules();
    jest.mock('dotenv', () => ({ config: jest.fn() }));

    const cfg = require('../config');
    expect(cfg.INTEGRATION_HUB_PORT).toBe(3000);
    expect(cfg.INTEGRATION_HUB_HOST).toBe('0.0.0.0');
    expect(cfg.DATAHUB_PORT).toBe(8080);
    expect(cfg.DATAHUB_HOST).toBe('localhost');
    expect(cfg.DATAHUB_GRAPHQL_ENDPOINT).toBe('http://localhost:8080/api/graphql');
    expect(cfg.DATAHUB_TOKEN).toBeUndefined();
  });

  it('uses env vars when set', () => {
    process.env.INTEGRATION_HUB_PORT = '1234';
    process.env.INTEGRATION_HUB_HOST = '127.0.0.1';
    process.env.DATAHUB_PORT = '5678';
    process.env.DATAHUB_HOST = 'datahubhost';
    process.env.DATAHUB_TOKEN = 'sometoken';
    jest.resetModules();
    const cfg = require('../config');
    expect(cfg.INTEGRATION_HUB_PORT).toBe(1234);
    expect(cfg.INTEGRATION_HUB_HOST).toBe('127.0.0.1');
    expect(cfg.DATAHUB_PORT).toBe(5678);
    expect(cfg.DATAHUB_HOST).toBe('datahubhost');
    expect(cfg.DATAHUB_GRAPHQL_ENDPOINT).toBe('http://datahubhost:5678/api/graphql');
    expect(cfg.DATAHUB_TOKEN).toBe('sometoken');
  });
}); 