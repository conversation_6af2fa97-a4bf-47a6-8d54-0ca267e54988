interface RawMetricsItem {
  urn: string;
  name: string;
  description: string;
  customProperties?: Array<{ key: string; value: string }>;
}

interface RawMetricsResponse {
  start: number;
  count: number;
  total: number;
  results?: RawMetricsItem[];
}

export interface TransformedMetricsItem {
  id: string;
  title: string;
  description: string;
  domains: string[];
  unit?: string;
  owningEntity?: string;
  endorsedBy?: string[];
  acronym?: string;
  reviewedBy?: string;
  formula?: string;
}

export interface TransformedMetricsResponse {
  offset: number;
  limit: number;
  total: number;
  results: TransformedMetricsItem[];
}

export function transformMetricsResponse(raw: RawMetricsResponse): TransformedMetricsResponse {
  const results = (raw.results || [])
    .map(item => {
      // Initialize variables to store custom property values
      let departments: string[] = [];
      let owningEntity: string | undefined = undefined;
      let endorsedBy: string[] = [];
      let acronym: string | undefined = undefined;

      // Process each custom property based on its key
      if (item.customProperties && item.customProperties.length > 0) {
        item.customProperties.forEach(prop => {
          if (prop.key === 'departments' && prop.value) {
            departments = prop.value.split(',').map(s => s.trim());
          } else if (prop.key === 'owningEntity') {
            owningEntity = prop.value;
          } else if (prop.key === 'endorsedBy') {
            endorsedBy = prop.value.split(',').map(s => s.trim());
          } else if (prop.key === 'acronym') {
            acronym = prop.value;
          }
        });
      }

      return {
        id: item.urn.replace('urn:li:glossaryTerm:', ''),
        title: item.name,
        description: item.description,
        domains: departments,
        unit: item.customProperties?.find(prop => prop.key === 'unit')?.value,
        owningEntity,
        endorsedBy: endorsedBy.length > 0 ? endorsedBy : undefined,
        acronym
      };
    });
  return {
    offset: raw.start,
    limit: raw.count,
    total: raw.total,
    results
  };
}