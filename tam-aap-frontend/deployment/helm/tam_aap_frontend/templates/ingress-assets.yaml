{{- if .Values.ingress.enabled -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "tam-aap-frontend.fullname" . }}-assets
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: {{ include "tam-aap-frontend.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
  annotations: {}
spec:
  {{- if .Values.ingress.className }}
  ingressClassName: {{ .Values.ingress.className }}
  {{- end }}
  rules:
    - http:
        paths:
          - path: "/_next"
            pathType: Prefix
            backend:
              service:
                name: {{ include "tam-aap-frontend.fullname" . }}
                port:
                  number: {{ .Values.service.port }}


{{- end }}
