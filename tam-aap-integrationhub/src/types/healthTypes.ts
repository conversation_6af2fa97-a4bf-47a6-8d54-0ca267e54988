import { Type } from '@sinclair/typebox';

export const HealthSchema = {
  tags: ['Health'],
  summary: 'Health check endpoint',
  description: 'Check if the service is running properly',
  response: {
    200: Type.Object({
      status: Type.String()
    })
  }
};

export const RootSchema = {
  tags: ['Health'],
  summary: 'Root endpoint',
  description: 'Welcome message for the Integration Hub API',
  response: {
    200: Type.Object({
      message: Type.String()
    })
  }
}; 