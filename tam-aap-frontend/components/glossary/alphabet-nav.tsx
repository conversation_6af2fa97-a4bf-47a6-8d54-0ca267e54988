"use client";

interface AlphabetNavProps {
  activeLetter: string;
  onLetterClick: (letter: string) => void;
}

export default function AlphabetNav({
  activeLetter,
  onLetterClick,
}: AlphabetNavProps) {
  const ALPHABET = ["All", ..."ABCDEFGHIJKLMNOPQRSTUVWXYZ".split("")];

  return (
    <div className="self-stretch py-4 border-t border-b border-border flex flex-wrap justify-between items-center">
      {ALPHABET.map((letter) => {
        const isActive = activeLetter === letter;

        return (
          <button
            key={letter}
            className={`justify-center text-xl ${
              isActive
                ? "text-white font-semibold"
                : "text-zinc-600 font-light hover:text-zinc-400"
            } leading-normal`}
            onClick={() => onLetterClick(letter)}
          >
            {letter}
          </button>
        );
      })}
    </div>
  );
}
