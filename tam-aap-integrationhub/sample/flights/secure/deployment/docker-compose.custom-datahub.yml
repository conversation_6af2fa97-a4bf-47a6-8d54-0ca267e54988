networks:
  default:
    name: custom_datahub_network
services:
  broker:
    depends_on:
      zookeeper:
        condition: service_healthy
    environment:
    - KAFKA_BROKER_ID=1
    - KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181
    - KAFKA_LISTENER_SECURITY_PROTOCOL_MAP=PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
    - KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://broker:29092,PLAINTEXT_HOST://localhost:9092
    - KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1
    - KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS=0
    - KAFKA_HEAP_OPTS=-Xms256m -Xmx256m
    - KAFKA_CONFLUENT_SUPPORT_METRICS_ENABLE=false
    - KAFKA_MESSAGE_MAX_BYTES=5242880
    - KAFKA_MAX_MESSAGE_BYTES=5242880
    healthcheck:
      interval: 1s
      retries: 5
      start_period: 60s
      test: nc -z broker $${DATAHUB_KAFKA_BROKER_PORT:-9092}
      timeout: 5s
    hostname: broker
    image: ${DATAHUB_CONFLUENT_KAFKA_IMAGE:-confluentinc/cp-kafka}:${DATAHUB_CONFLUENT_VERSION:-7.4.0}
    ports:
    - ${DATAHUB_MAPPED_KAFKA_BROKER_PORT:-9092}:9092
    volumes:
    - custom_broker:/var/lib/kafka/data/
  datahub-actions:
    depends_on:
      datahub-gms:
        condition: service_healthy
    environment:
    - ACTIONS_CONFIG=${ACTIONS_CONFIG:-}
    - ACTIONS_EXTRA_PACKAGES=${ACTIONS_EXTRA_PACKAGES:-}
    - DATAHUB_GMS_HOST=datahub-gms
    - DATAHUB_GMS_PORT=8080
    - DATAHUB_GMS_PROTOCOL=http
    - DATAHUB_SYSTEM_CLIENT_ID=__datahub_system
    - DATAHUB_SYSTEM_CLIENT_SECRET=JohnSnowKnowsNothing
    - METADATA_SERVICE_AUTH_ENABLED=true
    - KAFKA_BOOTSTRAP_SERVER=broker:29092
    - KAFKA_PROPERTIES_SECURITY_PROTOCOL=PLAINTEXT
    - METADATA_AUDIT_EVENT_NAME=MetadataAuditEvent_v4
    - METADATA_CHANGE_LOG_VERSIONED_TOPIC_NAME=MetadataChangeLog_Versioned_v1
    - SCHEMA_REGISTRY_URL=http://schema-registry:8081
    hostname: actions
    image: ${DATAHUB_ACTIONS_IMAGE:-acryldata/datahub-actions}:${DATAHUB_VERSION:-head}-slim
  datahub-frontend-react:
    depends_on:
      datahub-gms:
        condition: service_healthy
    environment:
    - DATAHUB_GMS_HOST=datahub-gms
    - DATAHUB_GMS_PORT=8080
    - DATAHUB_SECRET=YouKnowNothing
    - DATAHUB_APP_VERSION=1.0
    - DATAHUB_PLAY_MEM_BUFFER_SIZE=10MB
    - AUTH_OIDC_ENABLED=false
    - AUTH_TOKEN_ENABLED=true
    - JAVA_OPTS=-Xms512m -Xmx512m -Dhttp.port=9002 -Dconfig.file=datahub-frontend/conf/application.conf -Djava.security.auth.login.config=datahub-frontend/conf/jaas.conf -Dlogback.configurationFile=datahub-frontend/conf/logback.xml -Dlogback.debug=false -Dpidfile.path=/dev/null
    - KAFKA_BOOTSTRAP_SERVER=broker:29092
    - DATAHUB_TRACKING_TOPIC=DataHubUsageEvent_v1
    - ELASTIC_CLIENT_HOST=elasticsearch
    - ELASTIC_CLIENT_PORT=9200
    hostname: datahub-frontend-react
    image: ${DATAHUB_FRONTEND_IMAGE:-acryldata/datahub-frontend-react}:${DATAHUB_VERSION:-head}
    ports:
    - ${DATAHUB_MAPPED_FRONTEND_PORT:-9002}:9002
    volumes:
    - ${HOME}/.datahub/plugins:/etc/datahub/plugins
  datahub-gms:
    depends_on:
      datahub-upgrade:
        condition: service_completed_successfully
    environment:
    - DATAHUB_SERVER_TYPE=${DATAHUB_SERVER_TYPE:-quickstart}
    - DATAHUB_TELEMETRY_ENABLED=${DATAHUB_TELEMETRY_ENABLED:-true}
    - DATAHUB_UPGRADE_HISTORY_KAFKA_CONSUMER_GROUP_ID=generic-duhe-consumer-job-client-gms
    - EBEAN_DATASOURCE_DRIVER=com.mysql.jdbc.Driver
    - EBEAN_DATASOURCE_HOST=mysql:3306
    - EBEAN_DATASOURCE_PASSWORD=datahub
    - EBEAN_DATASOURCE_URL=****************************************************************************************************************&enabledTLSProtocols=TLSv1.2
    - EBEAN_DATASOURCE_USERNAME=datahub
    - ELASTICSEARCH_HOST=elasticsearch
    - ELASTICSEARCH_INDEX_BUILDER_MAPPINGS_REINDEX=true
    - ELASTICSEARCH_INDEX_BUILDER_SETTINGS_REINDEX=true
    - ELASTICSEARCH_PORT=9200
    - ENTITY_REGISTRY_CONFIG_PATH=/datahub/datahub-gms/resources/entity-registry.yml
    - ENTITY_SERVICE_ENABLE_RETENTION=true
    - ES_BULK_REFRESH_POLICY=WAIT_UNTIL
    - GRAPH_SERVICE_DIFF_MODE_ENABLED=true
    - GRAPH_SERVICE_IMPL=${GRAPH_SERVICE_IMPL:-elasticsearch}
    - JAVA_OPTS=-Xms1g -Xmx2g
    - KAFKA_BOOTSTRAP_SERVER=broker:29092
    - KAFKA_CONSUMER_STOP_ON_DESERIALIZATION_ERROR=${KAFKA_CONSUMER_STOP_ON_DESERIALIZATION_ERROR:-true}
    - KAFKA_SCHEMAREGISTRY_URL=http://schema-registry:8081
    - MAE_CONSUMER_ENABLED=true
    - MCE_CONSUMER_ENABLED=true
    - METADATA_SERVICE_AUTH_ENABLED=${METADATA_SERVICE_AUTH_ENABLED:-true}
    - NEO4J_HOST=http://neo4j:7474
    - NEO4J_PASSWORD=datahub
    - NEO4J_URI=bolt://neo4j
    - NEO4J_USERNAME=neo4j
    - PE_CONSUMER_ENABLED=true
    - THEME_V2_DEFAULT=true
    - UI_INGESTION_ENABLED=true
    - DATAHUB_SYSTEM_CLIENT_ID=__datahub_system
    - DATAHUB_SYSTEM_CLIENT_SECRET=JohnSnowKnowsNothing
    - TOKEN_SERVICE_SALT=YouKnowNothing
    - TOKEN_SERVICE_SIGNING_KEY=YOUKNOWNOTHINGJONSNOW
    - TOKEN_SERVICE_SIGNING_ALG=HS256
    healthcheck:
      interval: 1s
      retries: 3
      start_period: 90s
      test: curl -sS --fail http://datahub-gms:${DATAHUB_GMS_PORT:-8080}/health
      timeout: 5s
    hostname: datahub-gms
    image: ${DATAHUB_GMS_IMAGE:-acryldata/datahub-gms}:${DATAHUB_VERSION:-head}
    ports:
    - ${DATAHUB_MAPPED_GMS_PORT:-8080}:8080
    volumes:
    - ${HOME}/.datahub/plugins:/etc/datahub/plugins
  datahub-upgrade:
    command:
    - -u
    - SystemUpdate
    depends_on:
      elasticsearch-setup:
        condition: service_completed_successfully
      kafka-setup:
        condition: service_completed_successfully
      mysql-setup:
        condition: service_completed_successfully
      neo4j:
        condition: service_healthy
    environment:
    - BACKFILL_BROWSE_PATHS_V2=true
    - DATAHUB_GMS_HOST=datahub-gms
    - DATAHUB_GMS_PORT=8080
    - EBEAN_DATASOURCE_DRIVER=com.mysql.jdbc.Driver
    - EBEAN_DATASOURCE_HOST=mysql:3306
    - EBEAN_DATASOURCE_PASSWORD=datahub
    - EBEAN_DATASOURCE_URL=****************************************************************************************************************
    - EBEAN_DATASOURCE_USERNAME=datahub
    - ELASTICSEARCH_BUILD_INDICES_CLONE_INDICES=false
    - ELASTICSEARCH_HOST=elasticsearch
    - ELASTICSEARCH_INDEX_BUILDER_MAPPINGS_REINDEX=true
    - ELASTICSEARCH_INDEX_BUILDER_SETTINGS_REINDEX=true
    - ELASTICSEARCH_PORT=9200
    - ENTITY_REGISTRY_CONFIG_PATH=/datahub/datahub-gms/resources/entity-registry.yml
    - GRAPH_SERVICE_IMPL=${GRAPH_SERVICE_IMPL:-elasticsearch}
    - KAFKA_BOOTSTRAP_SERVER=broker:29092
    - KAFKA_SCHEMAREGISTRY_URL=http://schema-registry:8081
    - REPROCESS_DEFAULT_BROWSE_PATHS_V2=false
    hostname: datahub-upgrade
    image: ${DATAHUB_UPGRADE_IMAGE:-acryldata/datahub-upgrade}:${DATAHUB_VERSION:-head}
    labels:
      datahub_setup_job: true
  elasticsearch:
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
    environment:
    - discovery.type=single-node
    - ${XPACK_SECURITY_ENABLED:-xpack.security.enabled=false}
    - ES_JAVA_OPTS=-Xms512m -Xmx1g -Dlog4j2.formatMsgNoLookups=true
    - OPENSEARCH_JAVA_OPTS=-Xms512m -Xmx1g -Dlog4j2.formatMsgNoLookups=true
    healthcheck:
      interval: 1s
      retries: 3
      start_period: 20s
      test: curl -sS --fail http://elasticsearch:$${DATAHUB_ELASTIC_PORT:-9200}/_cluster/health?wait_for_status=yellow&timeout=0s
      timeout: 5s
    hostname: elasticsearch
    image: ${DATAHUB_SEARCH_IMAGE:-elasticsearch}:${DATAHUB_SEARCH_TAG:-7.10.1}
    ports:
    - ${DATAHUB_MAPPED_ELASTIC_PORT:-9200}:9200
    volumes:
    - custom_esdata:/usr/share/elasticsearch/data
  elasticsearch-setup:
    depends_on:
      elasticsearch:
        condition: service_healthy
    environment:
    - ELASTICSEARCH_USE_SSL=${ELASTICSEARCH_USE_SSL:-false}
    - USE_AWS_ELASTICSEARCH=${USE_AWS_ELASTICSEARCH:-false}
    - ELASTICSEARCH_HOST=elasticsearch
    - ELASTICSEARCH_PORT=9200
    - ELASTICSEARCH_PROTOCOL=http
    hostname: elasticsearch-setup
    image: ${DATAHUB_ELASTIC_SETUP_IMAGE:-acryldata/datahub-elasticsearch-setup}:${DATAHUB_VERSION:-head}
    labels:
      datahub_setup_job: true
  kafka-setup:
    depends_on:
      broker:
        condition: service_healthy
      schema-registry:
        condition: service_healthy
    environment:
    - DATAHUB_PRECREATE_TOPICS=${DATAHUB_PRECREATE_TOPICS:-false}
    - KAFKA_BOOTSTRAP_SERVER=broker:29092
    - KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181
    - USE_CONFLUENT_SCHEMA_REGISTRY=TRUE
    hostname: kafka-setup
    image: ${DATAHUB_KAFKA_SETUP_IMAGE:-acryldata/datahub-kafka-setup}:${DATAHUB_VERSION:-head}
    labels:
      datahub_setup_job: true
  mysql:
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_bin --default-authentication-plugin=mysql_native_password
    environment:
    - MYSQL_DATABASE=datahub
    - MYSQL_USER=datahub
    - MYSQL_PASSWORD=datahub
    - MYSQL_ROOT_PASSWORD=datahub
    - MYSQL_ROOT_HOST=%
    healthcheck:
      interval: 1s
      retries: 3
      start_period: 10s
      test: mysqladmin ping -h mysql -u $$MYSQL_USER --password=$$MYSQL_PASSWORD
      timeout: 5s
    hostname: mysql
    image: mysql:${DATAHUB_MYSQL_VERSION:-8.2}
    ports:
    - ${DATAHUB_MAPPED_MYSQL_PORT:-3306}:3306
    restart: on-failure
    volumes:
    - custom_mysqldata:/var/lib/mysql
  mysql-setup:
    depends_on:
      mysql:
        condition: service_healthy
    environment:
    - MYSQL_HOST=mysql
    - MYSQL_PORT=3306
    - MYSQL_USERNAME=datahub
    - MYSQL_PASSWORD=datahub
    - DATAHUB_DB_NAME=datahub
    hostname: mysql-setup
    image: ${DATAHUB_MYSQL_SETUP_IMAGE:-acryldata/datahub-mysql-setup}:${DATAHUB_VERSION:-head}
    labels:
      datahub_setup_job: true
  neo4j:
    environment:
    - NEO4J_AUTH=neo4j/datahub
    - NEO4J_dbms_default__database=graph.db
    - NEO4J_dbms_allow__upgrade=true
    - NEO4JLABS_PLUGINS=["apoc"]
    healthcheck:
      interval: 1s
      retries: 5
      start_period: 5s
      test: wget http://neo4j:$${DATAHUB_NEO4J_HTTP_PORT:-7474}
      timeout: 5s
    hostname: neo4j
    image: neo4j:4.4.9-community
    ports:
    - ${DATAHUB_MAPPED_NEO4J_HTTP_PORT:-7474}:7474
    - ${DATAHUB_MAPPED_NEO4J_BOLT_PORT:-7687}:7687
    volumes:
    - custom_neo4jdata:/data
  schema-registry:
    depends_on:
      broker:
        condition: service_healthy
    environment:
    - SCHEMA_REGISTRY_HOST_NAME=schemaregistry
    - SCHEMA_REGISTRY_KAFKASTORE_SECURITY_PROTOCOL=PLAINTEXT
    - SCHEMA_REGISTRY_KAFKASTORE_BOOTSTRAP_SERVERS=broker:29092
    healthcheck:
      interval: 1s
      retries: 3
      start_period: 60s
      test: nc -z schema-registry ${DATAHUB_SCHEMA_REGISTRY_PORT:-8081}
      timeout: 5s
    hostname: schema-registry
    image: ${DATAHUB_CONFLUENT_SCHEMA_REGISTRY_IMAGE:-confluentinc/cp-schema-registry}:${DATAHUB_CONFLUENT_VERSION:-7.4.0}
    ports:
    - ${DATAHUB_MAPPED_SCHEMA_REGISTRY_PORT:-8081}:8081
  zookeeper:
    environment:
    - ZOOKEEPER_CLIENT_PORT=2181
    - ZOOKEEPER_TICK_TIME=2000
    healthcheck:
      interval: 5s
      retries: 3
      start_period: 10s
      test: echo srvr | nc zookeeper $${DATAHUB_ZK_PORT:-2181}
      timeout: 5s
    hostname: zookeeper
    image: ${DATAHUB_CONFLUENT_ZOOKEEPER_IMAGE:-confluentinc/cp-zookeeper}:${DATAHUB_CONFLUENT_VERSION:-7.4.0}
    ports:
    - ${DATAHUB_MAPPED_ZK_PORT:-2181}:2181
    volumes:
    - custom_zkdata:/var/lib/zookeeper/data
    - custom_zklogs:/var/lib/zookeeper/log
volumes:
  custom_broker: null
  custom_esdata: null
  custom_mysqldata: null
  custom_neo4jdata: null
  custom_zkdata: null
  custom_zklogs: null
