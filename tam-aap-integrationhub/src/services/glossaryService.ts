import { FastifyRequest } from 'fastify';
import { DataHubError } from '../types/fastifyTypes.js';
import {
  GlossarySearchData,
  GlossarySearchResponse,
  GlossaryTermResponse,
  GlossaryTermEntity,
  GlossarySearchFilters
} from '../types/glossaryTypes.js';
import { buildGlossarySearchQuery, buildGetGlossaryTermByUrnQuery } from '../utils/glossaryQueryBuilder.js';
import { buildDomainUrnSearchQuery } from '../utils/domainQueryBuilder.js';
import { handleGraphQLErrors } from '../utils/graphqlErrorHandler.js';

// Helper: fetch all glossary results by paginating
async function fetchAllGlossaryResults(request: FastifyRequest, queryText: string, filtersClause: string): Promise<any[]> {
  let allResults: any[] = [];
  let pageStart = 0;
  const pageSize = 1000;
  while (true) {
    const query = buildGlossarySearchQuery(queryText, pageStart, pageSize, filtersClause);
    const response = await request.datahub.query<GlossarySearchData>(query);
    handleGraphQLErrors(response, `fetching glossary results (page ${pageStart / pageSize + 1})`);
    const searchResults = response.data.search.searchResults || [];
    allResults = allResults.concat(searchResults);
    if (allResults.length >= response.data.search.total || searchResults.length === 0) break;
    pageStart += pageSize;
  }
  return allResults;
}

export async function searchGlossaryTerms(
  request: FastifyRequest,
  filters: GlossarySearchFilters
): Promise<GlossarySearchResponse> {
  let filtersClause = '';
  const filterArr: string[] = [];

  // Domain name to URN resolution
  if (filters.domainName && filters.domainName.length > 0) {
    const domainQuery = buildDomainUrnSearchQuery(filters.domainName);
    const domainResponse = await request.datahub.query<{ search: { searchResults: Array<{ entity: { urn: string } }> } }>(domainQuery);
    handleGraphQLErrors(domainResponse, `fetching domain URN for domainName "${filters.domainName}"`);
    const domainUrn = domainResponse?.data?.search?.searchResults?.[0]?.entity?.urn;
    /* istanbul ignore next */
    if (!domainUrn) {
      const error = new Error(`Domain with name '${filters.domainName}' not found`) as DataHubError;
      error.details = [`No domain found for name: ${filters.domainName}`];
      throw error;
    }
    filterArr.push(`{ field: "domains", values: ["${domainUrn}"] }`);
  }

  if (filterArr.length > 0) {
    filtersClause = `filters: [ ${filterArr.join(', ')} ],`;
  }

  const queryText = filters.search && typeof filters.search === 'string' && filters.search.length > 0 ? filters.search : '*';

  if (typeof filters.count !== 'number') {
    const allResults = await fetchAllGlossaryResults(request, queryText, filtersClause);
    // Build a response object similar to the original
    return {
      data: {
        search: {
          start: 0,
          count: allResults.length,
          total: allResults.length,
          searchResults: allResults
        }
      }
    };
  }

  const query = buildGlossarySearchQuery(
    queryText,
    typeof filters.start === 'number' ? filters.start : 0,
    typeof filters.count === 'number' ? filters.count : 1000,
    filtersClause
  );

  const response = await request.datahub.query<GlossarySearchData>(query);
  handleGraphQLErrors(response, `searching glossary terms with query "${queryText}"`);

  // Just return the raw results; filtering will be done in the controller
  const searchResponse: GlossarySearchResponse = {
    data: {
      search: {
        start: response.data.search.start,
        count: response.data.search.count,
        total: response.data.search.total,
        /* istanbul ignore next */
        searchResults: response.data.search.searchResults || []
      }
    }
  };

  return searchResponse;
}

/**
 * Fetches a glossary term by its URN
 * @param request The Fastify request object with DataHub client
 * @param urn The URN of the glossary term to fetch
 * @returns The glossary term entity
 */
export async function getGlossaryTermByUrn(request: FastifyRequest, urn: string): Promise<GlossaryTermEntity> {
  const query = buildGetGlossaryTermByUrnQuery(urn);
  const response = await request.datahub.query<GlossaryTermResponse>(query);
  handleGraphQLErrors(response, `fetching glossary term with URN "${urn}"`);
  return response.data.glossaryTerm;
}