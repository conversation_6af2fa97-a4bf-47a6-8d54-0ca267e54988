import { buildDomainSearchQuery, buildGetDomainByUrnQuery, escapeGraphQLString, buildDomainUrnSearchQuery } from '../domainQueryBuilder.js';

describe('domainQueryBuilder', () => {
  describe('escapeGraphQLString', () => {
    it('escapes double quotes', () => {
      expect(escapeGraphQLString('test "quoted" string')).toBe('test \\"quoted\\" string');
    });

    it('escapes backslashes', () => {
      expect(escapeGraphQLString('test\\with\\backslashes')).toBe('test\\\\with\\\\backslashes');
    });

    it('escapes newlines and other control characters', () => {
      expect(escapeGraphQLString('test\nwith\nnewlines')).toBe('test\\nwith\\nnewlines');
      expect(escapeGraphQLString('test\rwith\rreturns')).toBe('test\\rwith\\rreturns');
      expect(escapeGraphQLString('test\twith\ttabs')).toBe('test\\twith\\ttabs');
    });

    it('handles empty strings', () => {
      expect(escapeGraphQLString('')).toBe('');
    });
  });
  describe('buildDomainSearchQuery', () => {
    it('builds a valid GraphQL query for domain search', () => {
      const query = buildDomainSearchQuery('flight', 0, 10);

      // Check that the query contains the expected elements
      expect(query).toContain('type: DOMAIN');
      expect(query).toContain('query: "flight"');
      expect(query).toContain('start: 0');
      expect(query).toContain('count: 10');

      // Check for required fields in the response structure
      expect(query).toContain('start');
      expect(query).toContain('count');
      expect(query).toContain('total');
      expect(query).toContain('searchResults');
      expect(query).toContain('entity');
      expect(query).toContain('... on Domain');
      expect(query).toContain('urn');
      expect(query).toContain('properties');
      expect(query).toContain('name');
      expect(query).toContain('description');
    });

    it('escapes special characters in the query text', () => {
      const queryWithQuotes = buildDomainSearchQuery('flight "operations"', 0, 10);
      expect(queryWithQuotes).toContain('query: "flight \\"operations\\""');

      const queryWithBackslash = buildDomainSearchQuery('flight\\operations', 0, 10);
      expect(queryWithBackslash).toContain('query: "flight\\\\operations"');

      const queryWithNewlines = buildDomainSearchQuery('flight\noperations', 0, 10);
      expect(queryWithNewlines).toContain('query: "flight\\noperations"');
    });

    it('handles pagination parameters correctly', () => {
      const query = buildDomainSearchQuery('flight', 20, 50);
      expect(query).toContain('start: 20');
      expect(query).toContain('count: 50');
    });
  });

  describe('buildGetDomainByUrnQuery', () => {
    it('builds a valid GraphQL query for fetching a domain by URN', () => {
      const urn = 'urn:li:domain:flightOperations';
      const query = buildGetDomainByUrnQuery(urn);

      // Check that the query contains the expected elements
      expect(query).toContain(`domain(urn: "${urn}")`);

      // Check for required fields in the response structure
      expect(query).toContain('urn');
      expect(query).toContain('properties');
      expect(query).toContain('name');
      expect(query).toContain('description');
    });

    it('escapes special characters in the URN', () => {
      const urnWithQuotes = 'urn:li:domain:"flightOps"';
      const query = buildGetDomainByUrnQuery(urnWithQuotes);
      expect(query).toContain('domain(urn: "urn:li:domain:\\"flightOps\\""');

      const urnWithBackslash = 'urn:li:domain:flight\\ops';
      const queryWithBackslash = buildGetDomainByUrnQuery(urnWithBackslash);
      expect(queryWithBackslash).toContain('domain(urn: "urn:li:domain:flight\\\\ops"');

      const urnWithNewlines = 'urn:li:domain:flight\nops';
      const queryWithNewlines = buildGetDomainByUrnQuery(urnWithNewlines);
      expect(queryWithNewlines).toContain('domain(urn: "urn:li:domain:flight\\nops"');
    });
  });

  describe('buildDomainUrnSearchQuery', () => {
    it('builds a valid GraphQL query for searching a domain by name and returning its URN', () => {
      const query = buildDomainUrnSearchQuery('Flight Operations');
      expect(query).toContain('type: DOMAIN');
      expect(query).toContain('query: "Flight Operations"');
      expect(query).toContain('start: 0');
      expect(query).toContain('count: 1');
      expect(query).toContain('searchResults');
      expect(query).toContain('entity');
      expect(query).toContain('... on Domain');
      expect(query).toContain('urn');
    });

    it('escapes special characters in the domain name', () => {
      const query = buildDomainUrnSearchQuery('Flight "Ops"');
      expect(query).toContain('query: "Flight \\\"Ops\\\""');
    });
  });
});
