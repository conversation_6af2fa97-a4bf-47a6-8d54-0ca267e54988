replicaCount: 1

annotations:
  rollme: "{{ now | date \"20060102150405\" }}"

image:
  repository: airport-acumen-frontend
  tag: latest
  pullPolicy: IfNotPresent

service:
  type: LoadBalancer
  port: 3000
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-name: "tam-aap-frontend-lb"
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-scheme: "internet-facing"

ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/use-regex: "false"
  hosts:
    - paths:
        - path: "/frontend"
          pathType: Prefix
  tls: []

livenessProbe:
  enabled: true
  path: /frontend/api/health
  port: 3000

readinessProbe:
  enabled: true
  path: /frontend/api/health
  port: 3000

resources: {}

env:
  NODE_ENV: production
  PORT: 3000
  NEXT_PUBLIC_BASE_PATH: /frontend
  KEYCLOAK_CLIENT_ID: tam-aap-frontend
  KEYCLOAK_CLIENT_SECRET: your-keycloak-client-secret
  KEYCLOAK_ISSUER: https://your-keycloak-url/realms/your-realm
  BACKEND_URL: http://airport-acumen-backend.aap-tam:3020

imagePullSecrets:
# Add additional environment variables as needed