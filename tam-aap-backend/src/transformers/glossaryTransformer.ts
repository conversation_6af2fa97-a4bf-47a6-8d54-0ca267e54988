interface RawGlossaryItem {
  urn: string;
  name: string;
  description: string;
  customProperties?: Array<{ key: string; value: string }>;
}

interface RawGlossaryResponse {
  start: number;
  count: number;
  total: number;
  results?: RawGlossaryItem[];
}

export interface ReviewedBy {
  id: string;
  department: string;
  name: string;
}

export interface TransformedGlossaryItem {
  id: string;
  title: string;
  description: string;
  domains: string[];
  lastUpdated?: string;
  owningEntity?: string;
  acronym?: string;
  synonyms?: string[];
  reviewedBy?: ReviewedBy[];
  unit?: string;
}

export interface TransformedGlossaryResponse {
  offset: number;
  limit: number;
  total: number;
  results: TransformedGlossaryItem[];
}

export function transformGlossaryResponse(
  raw: RawGlossaryResponse,
  extended = false
): TransformedGlossaryResponse {
  const results = (raw.results?.filter(item =>
    item.customProperties && item.customProperties.some(prop => prop.key === 'type' && prop.value === 'glossary')
  ) || []).map((item) => {
    let departments: string[] = [];
    let lastUpdated = '';
    let owningEntity = '';
    let acronym = '';
    let synonyms: string[] = [];
    let reviewedBy: ReviewedBy[] = [];
    let unit: string | undefined;
    if (item.customProperties && item.customProperties.length > 0) {
      item.customProperties.forEach(prop => {
        if (prop.key === 'departments' && prop.value) {
          departments = prop.value.split(',').map(s => s.trim());
        } else if (prop.key === 'lastUpdated') {
          lastUpdated = prop.value;
        } else if (prop.key === 'owningEntity') {
          owningEntity = prop.value;
        } else if (prop.key === 'acronym') {
          acronym = prop.value;
        } else if (prop.key === 'synonyms' && prop.value) {
          synonyms = prop.value.split(',').map(s => s.trim());
        } else if (prop.key === 'reviewedBy' && prop.value) {
          reviewedBy = prop.value.split(',').map(reviewer => {
            const parts = reviewer.trim().split(':');
            if (parts.length === 3) {
              return {
                id: parts[0],
                department: parts[1],
                name: parts[2]
              };
            }
            return {
              id: '',
              department: '',
              name: reviewer.trim()
            };
          });
        } else if (prop.key === 'unit' && prop.value) {
          unit = prop.value;
        }
      });
    }
    if (extended) {
      return {
        id: item.urn.replace('urn:li:glossaryTerm:', ''),
        title: item.name,
        description: item.description,
        domains: departments,
        lastUpdated,
        owningEntity,
        acronym,
        synonyms,
        reviewedBy,
        unit
      };
    } else {
      return {
        id: item.urn.replace('urn:li:glossaryTerm:', ''),
        title: item.name,
        description: item.description,
        domains: departments,
        unit
      };
    }
  });

  return {
    offset: raw.start,
    limit: raw.count,
    total: raw.total,
    results
  };
}