{"info": {"_postman_id": "9914d191-0ed0-4ecb-839c-a1f101ca252d", "name": "IntegrationHub", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "38811471"}, "item": [{"name": "datahub all bussiness glossary", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", "", "", "pm.test(\"Response time is within an acceptable range\", function () {", "  pm.expect(pm.response.responseTime).to.be.below(200);", "});", "", "", "pm.test(\"Response has the required fields - start, count, total, and results\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData).to.have.property('start');", "    pm.expect(responseData).to.have.property('count');", "    pm.expect(responseData).to.have.property('total');", "    pm.expect(responseData).to.have.property('results');", "});", "", "", "pm.test(\"Results array and its elements are valid\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData.results).to.be.an('array').and.to.have.lengthOf.at.least(1, \"Results array should not be empty\");", "", "    responseData.results.forEach(function(result) {", "        pm.expect(result).to.be.an('object');", "        pm.expect(result.urn).to.exist.and.to.be.a('string', \"URN should be a string\");", "        pm.expect(result.name).to.exist.and.to.be.a('string', \"Name should be a string\");", "        pm.expect(result.description).to.exist.and.to.be.a('string', \"Description should be a string\");", "", "        if (result.customProperties) {", "            result.customProperties.forEach(function(customProperty) {", "                pm.expect(customProperty).to.be.an('object');", "                pm.expect(customProperty.key).to.exist.and.to.be.a('string', \"Custom property key should be a string\");", "                pm.expect(customProperty.value).to.exist.and.to.be.a('string', \"Custom property value should be a string\");", "            });", "        }", "    });", "});", "", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{integration_hub_url}}/dh-glossary", "host": ["{{integration_hub_url}}"], "path": ["dh-glossary"], "query": [{"key": "initialLetter", "value": "P", "disabled": true}, {"key": "start", "value": "1", "disabled": true}, {"key": "count", "value": "2", "disabled": true}, {"key": "search", "value": "Commercial air transport", "disabled": true}, {"key": "exactSearchMatch", "value": "true", "disabled": true}, {"key": "", "value": "", "disabled": true}]}}, "response": []}, {"name": "datahub all count and limit for business glossary", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", "", "", "pm.test(\"Response time is within an acceptable range\", function () {", "  pm.expect(pm.response.responseTime).to.be.below(200);", "});", "", "", "pm.test(\"Response has the required fields - start, count, total, and results\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData).to.have.property('start');", "    pm.expect(responseData).to.have.property('count');", "    pm.expect(responseData).to.have.property('total');", "    pm.expect(responseData).to.have.property('results');", "});", "", "", "pm.test(\"Results array and its elements are valid\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData.results).to.be.an('array').and.to.have.lengthOf.at.least(1, \"Results array should not be empty\");", "", "    responseData.results.forEach(function(result) {", "        pm.expect(result).to.be.an('object');", "        pm.expect(result.urn).to.exist.and.to.be.a('string', \"URN should be a string\");", "        pm.expect(result.name).to.exist.and.to.be.a('string', \"Name should be a string\");", "        pm.expect(result.description).to.exist.and.to.be.a('string', \"Description should be a string\");", "", "        if (result.customProperties) {", "            result.customProperties.forEach(function(customProperty) {", "                pm.expect(customProperty).to.be.an('object');", "                pm.expect(customProperty.key).to.exist.and.to.be.a('string', \"Custom property key should be a string\");", "                pm.expect(customProperty.value).to.exist.and.to.be.a('string', \"Custom property value should be a string\");", "            });", "        }", "    });", "});", "", "pm.test(\"Response respects 'start' and 'count' query params exactly\", function () {", "    const responseData = pm.response.json();", "    const url = pm.request.url.toString();", "    const urlParams = new URLSearchParams(url.split('?')[1]);", "", "    const requestedStart = parseInt(urlParams.get('start') || '0', 10);", "    const requestedCount = parseInt(urlParams.get('count') || '10', 10); // fallback default", "", "    // Validate 'start'", "    pm.expect(responseData).to.have.property('start');", "    pm.expect(responseData.start).to.eql(requestedStart);", "", "    // Validate 'count'", "    pm.expect(responseData).to.have.property('count');", "    pm.expect(responseData.count).to.eql(requestedCount);", "", "    // Validate 'results'", "    pm.expect(responseData).to.have.property('results');", "    pm.expect(responseData.results).to.be.an('array');", "    pm.expect(responseData.results.length, `Expected exactly ${requestedCount} results`).to.eql(requestedCount);", "});", "", "", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{integration_hub_url}}/dh-glossary?start=1&count=2", "host": ["{{integration_hub_url}}"], "path": ["dh-glossary"], "query": [{"key": "initialLetter", "value": "P", "disabled": true}, {"key": "start", "value": "1"}, {"key": "count", "value": "2"}, {"key": "search", "value": "Commercial air transport", "disabled": true}, {"key": "exactSearchMatch", "value": "true", "disabled": true}, {"key": "", "value": "", "disabled": true}]}}, "response": []}, {"name": "datahub search bussiness glossary", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", "", "", "pm.test(\"Response time is within an acceptable range\", function () {", "  pm.expect(pm.response.responseTime).to.be.below(200);", "});", "", "", "pm.test(\"Response has the required fields - start, count, total, and results\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData).to.have.property('start');", "    pm.expect(responseData).to.have.property('count');", "    pm.expect(responseData).to.have.property('total');", "    pm.expect(responseData).to.have.property('results');", "});", "", "", "pm.test(\"Results array and its elements are valid\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData.results).to.be.an('array').and.to.have.lengthOf.at.least(1, \"Results array should not be empty\");", "", "    responseData.results.forEach(function(result) {", "        pm.expect(result).to.be.an('object');", "        pm.expect(result.urn).to.exist.and.to.be.a('string', \"URN should be a string\");", "        pm.expect(result.name).to.exist.and.to.be.a('string', \"Name should be a string\");", "        pm.expect(result.description).to.exist.and.to.be.a('string', \"Description should be a string\");", "", "        if (result.customProperties) {", "            result.customProperties.forEach(function(customProperty) {", "                pm.expect(customProperty).to.be.an('object');", "                pm.expect(customProperty.key).to.exist.and.to.be.a('string', \"Custom property key should be a string\");", "                pm.expect(customProperty.value).to.exist.and.to.be.a('string', \"Custom property value should be a string\");", "            });", "        }", "    });", "});", "", "", "pm.test(\"Response contains the search query in name or description\", function () {", "    const responseData = pm.response.json();", "    ", "    // Manually extract 'search' param from the URL if needed", "    const url = pm.request.url.toString();", "    const urlParams = new URLSearchParams(url.split('?')[1]);", "    let searchQuery = urlParams.get('search');", "", "    searchQuery = searchQuery.replace(/%20/g, ' '); // just in case", "", "    responseData.results.forEach(function(result) {", "        const nameContainsQuery = result.name.toLowerCase().includes(searchQuery.toLowerCase());", "        const descriptionContainsQuery = result.description.toLowerCase().includes(searchQuery.toLowerCase());", "", "        pm.expect(nameContainsQuery || descriptionContainsQuery, `Search query \"${searchQuery}\" found in name or description`).to.be.true;", "    });", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{integration_hub_url}}/dh-glossary?search=Air taxi revenue&exactSearchMatch=true", "host": ["{{integration_hub_url}}"], "path": ["dh-glossary"], "query": [{"key": "initialLetter", "value": "P", "disabled": true}, {"key": "start", "value": "1", "disabled": true}, {"key": "count", "value": "2", "disabled": true}, {"key": "search", "value": "Air taxi revenue"}, {"key": "exactSearchMatch", "value": "true"}, {"key": "", "value": "", "disabled": true}]}}, "response": []}, {"name": "datahub glossary initial letter", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", "", "", "pm.test(\"Response time is within an acceptable range\", function () {", "  pm.expect(pm.response.responseTime).to.be.below(200);", "});", "", "", "pm.test(\"Response has the required fields - start, count, total, and results\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData).to.have.property('start');", "    pm.expect(responseData).to.have.property('count');", "    pm.expect(responseData).to.have.property('total');", "    pm.expect(responseData).to.have.property('results');", "});", "", "", "pm.test(\"Results array and its elements are valid\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData.results).to.be.an('array').and.to.have.lengthOf.at.least(1, \"Results array should not be empty\");", "", "    responseData.results.forEach(function(result) {", "        pm.expect(result).to.be.an('object');", "        pm.expect(result.urn).to.exist.and.to.be.a('string', \"URN should be a string\");", "        pm.expect(result.name).to.exist.and.to.be.a('string', \"Name should be a string\");", "        pm.expect(result.description).to.exist.and.to.be.a('string', \"Description should be a string\");", "", "        if (result.customProperties) {", "            result.customProperties.forEach(function(customProperty) {", "                pm.expect(customProperty).to.be.an('object');", "                pm.expect(customProperty.key).to.exist.and.to.be.a('string', \"Custom property key should be a string\");", "                pm.expect(customProperty.value).to.exist.and.to.be.a('string', \"Custom property value should be a string\");", "            });", "        }", "    });", "});", "", "", "pm.test(\"Each name starts with the initial letter provided as a query parameter\", function () {", "    const responseData = pm.response.json();", "    ", "    const initialLetter = pm.request.url.query.get('initialLetter').toLowerCase();", "    responseData.results.forEach(function(result) {", "        pm.expect(result.name.charAt(0).toLowerCase()).to.equal(initialLetter);", "    });", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"customProperties\": [\n        {\n            \"key\": \"type\",\n            \"value\": \"metrics\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{integration_hub_url}}/dh-glossary?initialLetter=A", "host": ["{{integration_hub_url}}"], "path": ["dh-glossary"], "query": [{"key": "initialLetter", "value": "A"}, {"key": "start", "value": "1", "disabled": true}, {"key": "count", "value": "2", "disabled": true}, {"key": "search", "value": "Commercial air transport", "disabled": true}, {"key": "exactSearchMatch", "value": "true", "disabled": true}, {"key": "", "value": "", "disabled": true}]}}, "response": []}, {"name": "datahub glossary with customProperties body", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", "", "", "pm.test(\"Response time is within an acceptable range\", function () {", "  pm.expect(pm.response.responseTime).to.be.below(200);", "});", "", "", "pm.test(\"Response has the required fields - start, count, total, and results\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData).to.have.property('start');", "    pm.expect(responseData).to.have.property('count');", "    pm.expect(responseData).to.have.property('total');", "    pm.expect(responseData).to.have.property('results');", "});", "", "", "pm.test(\"Results array and its elements are valid\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData.results).to.be.an('array').and.to.have.lengthOf.at.least(1, \"Results array should not be empty\");", "", "    responseData.results.forEach(function(result) {", "        pm.expect(result).to.be.an('object');", "        pm.expect(result.urn).to.exist.and.to.be.a('string', \"URN should be a string\");", "        pm.expect(result.name).to.exist.and.to.be.a('string', \"Name should be a string\");", "        pm.expect(result.description).to.exist.and.to.be.a('string', \"Description should be a string\");", "", "        if (result.customProperties) {", "            result.customProperties.forEach(function(customProperty) {", "                pm.expect(customProperty).to.be.an('object');", "                pm.expect(customProperty.key).to.exist.and.to.be.a('string', \"Custom property key should be a string\");", "                pm.expect(customProperty.value).to.exist.and.to.be.a('string', \"Custom property value should be a string\");", "            });", "        }", "    });", "});", "", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"customProperties\": [\n        {\n            \"key\": \"type\",\n            \"value\": \"metrics\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{integration_hub_url}}/dh-glossary", "host": ["{{integration_hub_url}}"], "path": ["dh-glossary"], "query": [{"key": "initialLetter", "value": "P", "disabled": true}, {"key": "start", "value": "1", "disabled": true}, {"key": "count", "value": "2", "disabled": true}, {"key": "search", "value": "Commercial air transport", "disabled": true}, {"key": "exactSearchMatch", "value": "true", "disabled": true}, {"key": "", "value": "", "disabled": true}]}}, "response": []}, {"name": "datahub domain", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "", "pm.test(\"Response time is within acceptable range\", function () {", "  pm.expect(pm.response.responseTime).to.be.below(200);", "});", "", "", "pm.test(\"Response schema has required fields\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData).to.have.property('start');", "    pm.expect(responseData).to.have.property('count');", "    pm.expect(responseData).to.have.property('total');", "    pm.expect(responseData).to.have.property('results');", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{integration_hub_url}}/dh-domain", "host": ["{{integration_hub_url}}"], "path": ["dh-domain"], "query": [{"key": "search", "value": "Flight%20Operations", "disabled": true}, {"key": "initialLetter", "value": "F", "disabled": true}, {"key": "exactSearchMatch", "value": "true", "disabled": true}, {"key": "start", "value": "1", "disabled": true}, {"key": "count", "value": "1", "disabled": true}]}}, "response": []}, {"name": "datahub domain search", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "  pm.expect(pm.response.code).to.equal(200);", "});", "", "", "pm.test(\"Response time is less than 200ms\", function () {", "  pm.expect(pm.response.responseTime).to.be.below(200);", "});", "", "", "pm.test(\"Validate the response schema for required fields\", function () {", "  const responseData = pm.response.json();", "  ", "  pm.expect(responseData).to.be.an('object');", "  pm.expect(responseData).to.have.property('start');", "  pm.expect(responseData).to.have.property('count');", "  pm.expect(responseData).to.have.property('total');", "  pm.expect(responseData).to.have.property('results');", "});", "", "pm.test(\"Each result has urn, name, and description\", function () {", "    const responseData = pm.response.json();", "", "    responseData.results.forEach(function(result, index) {", "        pm.expect(result, `Result at index ${index} should have 'urn'`).to.have.property('urn');", "        pm.expect(result, `Result at index ${index} should have 'name'`).to.have.property('name');", "        pm.expect(result, `Result at index ${index} should have 'description'`).to.have.property('description');", "    });", "});", "", "pm.test(\"Search query exists in name or description\", function () {", "    const responseData = pm.response.json();", "    const url = pm.request.url.toString();", "    const urlParams = new URLSearchParams(url.split('?')[1]);", "", "    let searchQuery = urlParams.get('search') || '';", "    searchQuery = decodeURIComponent(searchQuery).toLowerCase();", "", "    responseData.results.forEach(function(result) {", "        const nameContains = result.name.toLowerCase().includes(searchQuery);", "        const descriptionContains = result.description.toLowerCase().includes(searchQuery);", "        pm.expect(nameContains || descriptionContains, `Search query \"${searchQuery}\" should exist in name or description`).to.be.true;", "    });", "});", "", "", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{integration_hub_url}}/dh-domain?search=Flight%20Operations&exactSearchMatch=true", "host": ["{{integration_hub_url}}"], "path": ["dh-domain"], "query": [{"key": "search", "value": "Flight%20Operations"}, {"key": "initialLetter", "value": "F", "disabled": true}, {"key": "exactSearchMatch", "value": "true"}, {"key": "start", "value": "1", "disabled": true}, {"key": "count", "value": "1", "disabled": true}]}}, "response": []}, {"name": "datahub domain initialLetter", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "", "pm.test(\"Response time is within an acceptable range\", function () {", "  pm.expect(pm.response.responseTime).to.be.below(200);", "});", "", "", "pm.test(\"Response schema has required fields\", function () {", "    const responseData = pm.response.json();", "", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData.results).to.be.an('array');", "    ", "    responseData.results.forEach(function(result) {", "        pm.expect(result.urn).to.exist;", "        pm.expect(result.name).to.exist;", "        pm.expect(result.description).to.exist;", "    });", "});", "", "pm.test(\"Each result has urn, name, and description\", function () {", "    const responseData = pm.response.json();", "", "    responseData.results.forEach(function(result, index) {", "        pm.expect(result, `Result at index ${index} should have 'urn'`).to.have.property('urn');", "        pm.expect(result, `Result at index ${index} should have 'name'`).to.have.property('name');", "        pm.expect(result, `Result at index ${index} should have 'description'`).to.have.property('description');", "    });", "});", "", "pm.test(\"All result names start with the initial letter\", function () {", "    const responseData = pm.response.json();", "    const url = pm.request.url.toString();", "    const urlParams = new URLSearchParams(url.split('?')[1]);", "", "    const initialLetter = (urlParams.get('initialLetter') || '').toLowerCase();", "", "    pm.expect(initialLetter).to.not.be.empty;", "", "    responseData.results.forEach(function(result, index) {", "        const firstChar = result.name.charAt(0).toLowerCase();", "        pm.expect(firstChar, `Result at index ${index} should start with '${initialLetter}'`).to.eql(initialLetter);", "    });", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{integration_hub_url}}/dh-domain?initialLetter=F", "host": ["{{integration_hub_url}}"], "path": ["dh-domain"], "query": [{"key": "search", "value": "Flight%20Operations", "disabled": true}, {"key": "initialLetter", "value": "F"}, {"key": "exactSearchMatch", "value": "true", "disabled": true}, {"key": "start", "value": "1", "disabled": true}, {"key": "count", "value": "1", "disabled": true}]}}, "response": []}, {"name": "domain urn", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "", "pm.test(\"Response has the required fields - urn, name, and description\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData.urn).to.exist;", "    pm.expect(responseData.name).to.exist;", "    pm.expect(responseData.description).to.exist;", "});", "", "", "pm.test(\"Urn is a non-empty string\", function () {", "    const responseData = pm.response.json();", "", "    const urn = responseData.urn;", "    const queryUrn = pm.request.url.query.get(\"urn\");", "", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData.urn).to.be.a('string').and.to.have.lengthOf.at.least(1, \"Urn should not be empty\");", "    pm.expect(urn).to.equal(queryUrn, \"Urn in response should match the urn in the request query parameter\");", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{integration_hub_url}}/domain/urn?urn=urn:li:domain:bf8b3ac8-09b6-4015-a122-1784fc246d9e", "host": ["{{integration_hub_url}}"], "path": ["domain", "urn"], "query": [{"key": "urn", "value": "urn:li:domain:bf8b3ac8-09b6-4015-a122-1784fc246d9e"}]}}, "response": []}, {"name": "glossary urn", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", "", "", "pm.test(\"Response time is less than 200ms\", function () {", "  pm.expect(pm.response.responseTime).to.be.below(200);", "});", "", "", "pm.test(\"Response has the required fields - urn, name, description, and customProperties\", function () {", "    const responseData = pm.response.json();", "", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData.urn).to.exist;", "    pm.expect(responseData.name).to.exist;", "    pm.expect(responseData.description).to.exist;", "    pm.expect(responseData.customProperties).to.exist;", "});", "", "", "pm.test(\"CustomProperties array is present\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData.customProperties).to.exist.and.to.be.an('array');", "});", "", "", "pm.test(\"Custom properties key are non-empty strings\", function () {", "  const responseData = pm.response.json();", "", "  pm.expect(responseData.customProperties).to.be.an('array');", "  responseData.customProperties.forEach(function(property) {", "    pm.expect(property.key).to.be.a('string').and.to.have.lengthOf.at.least(1, \"Key should not be empty\");", "  });", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{integration_hub_url}}/glossary/urn?urn=urn:li:glossaryTerm:5b43a5fb61575095e2d575ac91a1f329", "host": ["{{integration_hub_url}}"], "path": ["glossary", "urn"], "query": [{"key": "urn", "value": "urn:li:glossaryTerm:5b43a5fb61575095e2d575ac91a1f329"}]}}, "response": []}]}