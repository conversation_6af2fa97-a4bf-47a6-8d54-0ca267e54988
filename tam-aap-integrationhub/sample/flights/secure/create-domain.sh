#!/bin/bash

# Check if token is provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 <token>"
    echo "Example: $0 eyJhbGciOiJIUzI1NiJ9..."
    exit 1
fi

TOKEN=$1

# Create a domain using the GraphQL API
curl -X POST http://localhost:8080/api/graphql \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "mutation createDomain($input: CreateDomainInput!) { createDomain(input: $input) }",
    "variables": {
      "input": {
        "name": "Flight Operations",
        "description": "Domain for flight operations and related business terms."
      }
    }
  }'
