// Utility functions for building GraphQL queries for domain search

/**
 * Escapes a string for use in a GraphQL query.
 * @param str The string to escape
 * @returns The escaped string
 */
export function escapeGraphQLString(str: string): string {
  return str
    .replace(/\\/g, '\\\\')
    .replace(/"/g, '\\"')
    .replace(/\n/g, '\\n')
    .replace(/\r/g, '\\r')
    .replace(/\t/g, '\\t');
}

/**
 * Builds a GraphQL query string for searching domains.
 * @param queryText The search text
 * @param start The start index for pagination
 * @param count The number of results to fetch
 * @returns GraphQL query string
 */
export function buildDomainSearchQuery(queryText: string, start: number, count: number): string {
  const escapedQuery = escapeGraphQLString(queryText);
  return `{
    search(input: {
      type: DOMAIN,
      query: "${escapedQuery}",
      start: ${start},
      count: ${count}
    }) {
      start
      count
      total
      searchResults {
        entity {
          ... on Domain {
            urn
            properties {
              name
              description
            }
          }
        }
      }
    }
  }`;
}

/**
 * Builds a GraphQL query string for fetching a domain by URN.
 * @param urn The domain URN
 * @returns GraphQL query string
 */
export function buildGetDomainByUrnQuery(urn: string): string {
  const escapedUrn = escapeGraphQLString(urn);
  return `{
    domain(urn: "${escapedUrn}") {
      urn
      properties {
        name
        description
      }
    }
  }`;
}

/**
 * Builds a GraphQL query string for searching a domain by name and returning its URN.
 * @param domainName The domain name to search for
 * @returns GraphQL query string
 */
export function buildDomainUrnSearchQuery(domainName: string): string {
  const escapedQuery = escapeGraphQLString(domainName);
  return `{
    search(input: {
      type: DOMAIN,
      query: "${escapedQuery}",
      start: 0,
      count: 1
    }) {
      searchResults {
        entity {
          ... on Domain {
            urn
          }
        }
      }
    }
  }`;
}