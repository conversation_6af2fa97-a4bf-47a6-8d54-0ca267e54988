import { FastifyPluginAsync } from 'fastify';
import { DomainSearchSchema, DomainUrnSchema } from '../types/domainTypes.js';
import { domainSearchController, getDomainByUrnController } from '../controllers/domainController.js';

const domainRoutes: FastifyPluginAsync = async (fastify) => {
  fastify.get('/dh-domain', {
    schema: DomainSearchSchema,
    handler: domainSearchController,
  });

  fastify.get('/domain/urn', {
    schema: DomainUrnSchema,
    handler: getDomainByUrnController,
  });
};

export default domainRoutes;