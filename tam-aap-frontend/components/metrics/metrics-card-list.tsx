import Link from "next/link";
import { MetricsItem } from "@/types/dataverse";
import { Card, CardContent } from "../ui/card";

interface GlossaryTermListProps {
  items: MetricsItem[];
}

export default function GlossaryTermList({ items }: GlossaryTermListProps) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
      {items.map((item) => (
        <Card key={item.id} className="p-4 rounded-2xl">
          <CardContent className="p-0 flex flex-col gap-2 h-full">
            <div className="flex justify-start items-center gap-4">
              <h1 className="text-white text-2xl font-semibold">
                {item.title}{" "}
                {item.acronym && (
                  <span className="text-white">({item.acronym})</span>
                )}
              </h1>
            </div>

            <p className="text-zinc-400">{item.description}</p>

            <div className="flex flex-col gap-2 mt-4">
              {cardDetailItem("Definition Owning Entity", item.owningEntity)}
              {cardDetailItem("Domain", item.domains?.join(", "))}
              {cardDetailItem("Unit", item.unit)}
              {cardDetailItem("Endorsed By", item.endorsedBy.join(", "))}
            </div>

            <div className="mt-4 flex justify-center">
              <Link
                href={`/dataverse/metrics/${item.id}`}
                className="text-blue-600 dark:text-blue-500 text-xl"
              >
                View Details
              </Link>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

function cardDetailItem(title: string, value: string) {
  return (
    <div className="p-2 bg-background ounded-lg flex justify-between items-center">
      <div className="justify-center text-sm font-light">{title}</div>
      <div className="justify-center text-zinc-400 text-sm font-normal">
        {value}
      </div>
    </div>
  );
}
