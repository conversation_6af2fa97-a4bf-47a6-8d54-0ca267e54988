import Fastify, { FastifyInstance, RawServerDefault } from 'fastify';
import { TypeBoxTypeProvider } from '@fastify/type-provider-typebox';
import swagger from '@fastify/swagger';
import swaggerUi from '@fastify/swagger-ui';
import cors from '@fastify/cors';
import glossaryRoutes from './routes/glossaryRoutes';
import metricsRoutes from './routes/metricsRoutes';
import healthRoutes from './routes/healthRoutes';
import { TAM_PORTAL_PORT, TAM_PORTAL_HOST } from './configs/configs';
import { IncomingMessage, ServerResponse } from 'http';
import { FastifyBaseLogger } from 'fastify/types/logger';
import { swaggerConfig, swaggerUiConfig } from './configs/swagger';
import { corsOptions } from './configs/cors';

type FastifyTypebox = FastifyInstance<
  RawServerDefault,
  IncomingMessage,
  ServerResponse,
  FastifyBaseLogger,
  TypeBoxTypeProvider
>;

export async function buildApp(): Promise<FastifyTypebox> {
  const isProd = process.env.NODE_ENV === 'production';
  let loggerOptions: boolean | object = true; // Default: standard JSON logger

  if (!isProd) {
    try {
      const pinoPrettyOptions = {
        transport: {
          target: 'pino-pretty',
          options: {
            colorize: true,
            translateTime: 'SYS:standard',
            ignore: 'pid,hostname'
          }
        }
      };
      // Test pino-pretty availability
      const testLogger = Fastify({ logger: pinoPrettyOptions }).log;
      testLogger.info('Pino-pretty transport available, using it for development logging.');
      loggerOptions = pinoPrettyOptions;
    } catch (e) {
      console.warn('pino-pretty not found or failed to initialize, falling back to default JSON logger in development.');
      loggerOptions = true;
    }
  }

  const app = Fastify({ logger: loggerOptions }).withTypeProvider<TypeBoxTypeProvider>();

  // Register CORS
  await app.register(cors, corsOptions);

  // Register Swagger
  await app.register(swagger, swaggerConfig);
  await app.register(swaggerUi, swaggerUiConfig);

  await app.register(healthRoutes);
  await app.register(glossaryRoutes);
  await app.register(metricsRoutes);
  return app as FastifyTypebox;
}

export async function start() {
  const app = await buildApp();
  try {
    await app.listen({ port: TAM_PORTAL_PORT, host: TAM_PORTAL_HOST });
    app.log.info(`Server listening on http://${TAM_PORTAL_HOST}:${TAM_PORTAL_PORT}`);
  } catch (err) {
    app.log.error(err);
    process.exit(1);
  }
}

// If run directly, start the server
if (require.main === module) {
  start();
}