import { FastifyRequest, FastifyReply } from 'fastify';
import { searchGlossary } from '../services/glossaryService';
import { transformGlossaryResponse, TransformedGlossaryResponse, TransformedGlossaryItem } from '../transformers/glossaryTransformer';
import { glossaryBodyValidationSchema, glossaryQuerySchema } from '../schemas/glossarySchema';
import type { Static } from '@sinclair/typebox';
import axios from 'axios';
import { INTEGRATION_HUB_HOST, INTEGRATION_HUB_PORT } from '../configs/configs';

type GlossaryQuery = Static<typeof glossaryQuerySchema>;

// Define interfaces for raw and transformed responses
interface RawGlossaryResponse {
  urn: string;
  name: string;
  description: string;
  customProperties?: Array<{ key: string; value: string }>;
}

// Function to process custom properties
function processCustomProperties(raw: RawGlossaryResponse): {
  departments: string[];
  lastUpdated?: string;
  owningEntity?: string;
  acronym?: string;
  synonyms?: string[];
  reviewedBy?: Array<{ id: string; department: string; name: string }>;
  unit?: string;
} {
  const departments: string[] = [];
  let lastUpdated: string | undefined;
  let owningEntity: string | undefined;
  let acronym: string | undefined;
  let synonyms: string[] | undefined;
  let reviewedBy: Array<{ id: string; department: string; name: string }> | undefined;
  let unit: string | undefined;

  if (raw.customProperties && raw.customProperties.length > 0) {
    raw.customProperties.forEach(prop => {
      switch (prop.key) {
        case 'departments':
          departments.push(...prop.value.split(',').map(s => s.trim()));
          break;
        case 'lastUpdated':
          lastUpdated = prop.value;
          break;
        case 'owningEntity':
          owningEntity = prop.value;
          break;
        case 'acronym':
          acronym = prop.value;
          break;
        case 'synonyms':
          synonyms = prop.value.split(',').map(s => s.trim());
          break;
        case 'reviewedBy':
          reviewedBy = prop.value.split(',').map(reviewer => {
            const [id, department, name] = reviewer.split(':');
            return { id, department, name };
          });
          break;
        case 'unit':
          unit = prop.value;
          break;
      }
    });
  }

  return { departments, lastUpdated, owningEntity, acronym, synonyms, reviewedBy, unit };
}

export async function glossaryHandler(
  request: FastifyRequest<{ Querystring: GlossaryQuery }>,
  reply: FastifyReply
): Promise<TransformedGlossaryResponse> {
  const { domainName, kind, exactSearchMatch, initialLetter, domain, search, offset, limit, maxItemsPerCharacters } = request.query;
  try {
    const raw = await searchGlossary({ search, domainName, offset, limit, exactSearchMatch, initialLetter });
    let data = transformGlossaryResponse(raw, kind === 'extended');

    // Filter by domain if provided
    if (domain) {
      const filteredResults = data.results.filter(item => Array.isArray(item.domains) && item.domains.includes(domain));
      data = {
        ...data,
        results: filteredResults,
        total: filteredResults.length
      };
    }

    // Apply maxItemsPerCharacters if provided
    if (typeof maxItemsPerCharacters === 'number' && maxItemsPerCharacters > 0) {
      const grouped: { [char: string]: any[] } = {};
      for (const item of data.results) {
        const firstChar = (item.title[0] || '').toUpperCase();
        if (!grouped[firstChar]) grouped[firstChar] = [];
        if (grouped[firstChar].length < maxItemsPerCharacters) {
          grouped[firstChar].push(item);
        }
      }
      const limitedResults = Object.values(grouped).flat();
      data = {
        ...data,
        results: limitedResults,
        total: limitedResults.length
      };
    }

    return data;
  } catch (err) {
    request.server.log.error(err);
    throw reply.code(502).send({ error: 'Failed to fetch glossary data' });
  }
}

export async function getGlossaryByIdHandler(
  request: FastifyRequest<{ Params: { id: string } }>,
  reply: FastifyReply
): Promise<TransformedGlossaryItem> {
  const { id } = request.params;
  try {
    const urn = `urn:li:glossaryTerm:${id}`;
    const response = await axios.get(
      `http://${INTEGRATION_HUB_HOST}:${INTEGRATION_HUB_PORT}/glossary/urn?urn=${encodeURIComponent(urn)}`
    );
    const raw: RawGlossaryResponse = response.data;

    const { departments, lastUpdated, owningEntity, acronym, synonyms, reviewedBy, unit } = processCustomProperties(raw);

    return {
      id: raw.urn.replace('urn:li:glossaryTerm:', ''),
      title: raw.name,
      description: raw.description,
      domains: departments,
      lastUpdated,
      owningEntity,
      acronym,
      synonyms,
      reviewedBy,
      unit
    };
  } catch (err) {
    request.server.log.error(err);
    throw reply.code(502).send({ error: 'Failed to fetch glossary term' });
  }
}