"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { fetchMetricsItemById } from "@/lib/api";
import { MetricsItem } from "@/types/dataverse";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { Card, CardContent } from "@/components/ui/card";
import GlossaryDetailsSkeleton from "@/components/glossary/glossary-details-skeleton";

export default function MetricsDetailsPage() {
  const { termId } = useParams() as { termId: string };
  const [metricItem, setMetricItem] = useState<MetricsItem | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const loadGlossaryItem = async () => {
      setLoading(true);
      try {
        const item = await fetchMetricsItemById(termId);
        setMetricItem(item);
      } catch (error) {
        console.error("Failed to load glossary item:", error);
      } finally {
        setLoading(false);
      }
    };

    if (termId) {
      loadGlossaryItem();
    }
  }, [termId]);

  if (loading) {
    return <GlossaryDetailsSkeleton />;
  }

  if (!metricItem) {
    return (
      <div className="container mx-auto px-6 py-12">
        <div className="text-center">
          <h1 className="text-2xl font-semibold mb-4">Metric Item Not Found</h1>
          <p className="text-muted-foreground mb-6">
            The metric item you are looking for could not be found.
          </p>
          <Link
            href="/dataverse/metrics"
            className="text-primary hover:underline flex items-center justify-center gap-2"
          >
            <span>Return to Metrics</span>
            <ArrowRight className="h-4 w-4" />
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="container mx-auto px-6 py-6">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/dataverse/metrics">Metrics</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{metricItem.title}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <Card className="bg-card card-foreground border-border rounded-lg overflow-hidden my-6 p-4">
          <CardContent className="p-0 flex flex-col gap-2 h-full">
            <div className="flex flex-col md:flex-row md:items-center justify-between">
              <div className="flex justify-start items-center gap-4">
                <h1 className="text-white text-3xl font-semibold">
                  {metricItem.title}
                </h1>
                <div className="flex gap-2">
                  {metricItem.domains.map((domain) => (
                    <div
                      key={`${metricItem.id}-${domain}`}
                      className="relative w-8 h-8"
                    >
                      <Image
                        src={`/svg/${domain}.svg`}
                        alt={domain}
                        fill
                        className="object-contain"
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <p className="text-zinc-400 text-xl">{metricItem.description}</p>
          </CardContent>
        </Card>

        <div className="mt-6 flex flex-col gap-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 w-full">
            <Card className="bg-card border-border rounded-lg overflow-hidden p-4">
              <CardContent className="p-0">
                <h3 className="text-zinc-400 mb-2">Acronym</h3>
                <p className="text-white text-xl font-medium">
                  {metricItem.acronym || "N/A"}
                </p>
              </CardContent>
            </Card>

            <Card className="bg-card border-border rounded-lg overflow-hidden p-4">
              <CardContent className="p-0">
                <h3 className="text-zinc-400 mb-2">Domain</h3>
                <p className="text-white text-xl font-medium">
                  {metricItem.domains.join(", ")}
                </p>
              </CardContent>
            </Card>

            <Card className="bg-card border-border rounded-lg overflow-hidden p-4">
              <CardContent className="p-0">
                <h3 className="text-zinc-400 mb-2">Unit</h3>
                <p className="text-white text-xl font-medium">
                  {metricItem.unit || "N/A"}
                </p>
              </CardContent>
            </Card>

            <Card className="bg-card border-border rounded-lg overflow-hidden p-4">
              <CardContent className="p-0">
                <h3 className="text-zinc-400 mb-2">Definition Owning Entity</h3>
                <p className="text-white text-xl font-medium">
                  {metricItem.owningEntity || "N/A"}
                </p>
              </CardContent>
            </Card>
          </div>

          <div>
            <h2 className="text-white text-xl font-medium mb-4">Formula</h2>
            <div className="p-4 w-full bg-card rounded-2xl inline-flex flex-col justify-start items-center gap-2">
              <p className="justify-center text-white text-xl font-medium leading-loose">
                {metricItem.formula || "N/A"}
              </p>
            </div>
          </div>

          <div className="mt-6">
            <div className="flex justify-between items-center">
              <h3 className="text-white text-xl font-medium">
                Reviewed and approved by
              </h3>

              <Link
                href="/dataverse/committees"
                className="text-blue-500 text-xl font-normal flex items-center gap-2 hover:underline justify-self-end"
              >
                Learn about our committees <ArrowRight className="h-4 w-4" />
              </Link>
            </div>

            <div className="flex flex-wrap gap-4 mt-4">
              {metricItem.reviewedBy?.map((reviewer) => (
                <Card className="py-9 w-60" key={reviewer.id}>
                  <CardContent className="px-6">
                    <div key={reviewer.id} className="relative w-8 h-8 mb-4">
                      <Image
                        src={`/svg/${reviewer.department}.svg`}
                        alt={reviewer.name}
                        fill
                        className="object-contain"
                      />
                    </div>

                    <p className="text-zinc-400 text-xl font-semibold">
                      {reviewer.name}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
