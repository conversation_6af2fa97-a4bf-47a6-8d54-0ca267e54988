name: airport-acumen-backend-workflow

on:
  pull_request:
    branches: [ "main" ]

jobs:
  code_checkout:
    runs-on: self-hosted
    outputs:
      YEAR: ${{ steps.current_year.outputs.YEAR }}
    steps:
      - name: code checkout
        uses: actions/checkout@v4

      - name: Get Year
        id: current_year
        run: |
          echo "YEAR=$(date +'%y')" >> $GITHUB_OUTPUT  


  unit_testing:
    runs-on: self-hosted
    needs: code_checkout
    steps:
      - name: Running Unit Tests
        run: |
          echo "Unit testing to be added."


  build_helm_chart:
    runs-on: self-hosted
    needs: code_checkout
    steps:
      - name: Helm Linting
        run: |
          helm lint deployment/helm/tam_aap_backend 

      - name: Helm Templating
        run: |
          helm template deployment/helm/tam_aap_backend --set fullnameOverride=airport-acumen-backend

      - name: Helm Upload
        run: |
          helm repo add --username ${{ vars.CI_REGISTRY_USER }} --password ${{ secrets.CI_REGISTRY_PASSWORD }} emma-helm ${{ vars.HELM_REPO }}
          helm nexus-push emma-helm ./deployment/helm/tam_aap_backend tam-aap-backend -u ${{ vars.CI_REGISTRY_USER }} -p ${{ secrets.CI_REGISTRY_PASSWORD }}
          helm search repo emma-helm

  build_docker_image:
    runs-on: self-hosted
    needs: code_checkout
    outputs:
      IMAGE_TAG: ${{ steps.set_tag.outputs.IMAGE_TAG }}
    steps:
      - name: Set Image Tag
        id: set_tag
        run: |
          IMAGE_TAG=${{ needs.code_checkout.outputs.YEAR }}.${{ vars.RELEASE_NUMBER }}.${{ github.run_number }}
          echo "IMAGE_TAG=$IMAGE_TAG" >> $GITHUB_OUTPUT

      - name: Login To Nexus Docker Registry
        run: |
          echo ${{ secrets.CI_REGISTRY_PASSWORD }} | docker login ${{ vars.CI_REGISTRY }} --username ${{ vars.CI_REGISTRY_USER }} --password-stdin

      - name: Build The Docker Image
        run: |
          docker build . --file Dockerfile --tag ${{ vars.CI_REGISTRY }}/${{ vars.PROJECT_NAME }}/${{ vars.COMPONENT_NAME }}:${{ needs.code_checkout.outputs.YEAR }}.${{ vars.RELEASE_NUMBER }}.${{ github.run_number }}

      - name: Push The Docker Image
        run: |
          docker push ${{ vars.CI_REGISTRY }}/${{ vars.PROJECT_NAME }}/${{ vars.COMPONENT_NAME }}:${{ needs.code_checkout.outputs.YEAR }}.${{ vars.RELEASE_NUMBER }}.${{ github.run_number }}


  deploying_helm:
    needs: [build_helm_chart,build_docker_image]
    # needs: [build_helm_chart]
    runs-on: self-hosted
    steps:
      - name: Set up AWS CLI
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.CICD_USER_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.CICD_USER_SECRET_KEY }}
          aws-region: ${{ vars.AWS_DEFAULT_REGION }}

      - name: Configure kubectl to use EKS
        run: |
          aws eks --region ${{ vars.AWS_DEFAULT_REGION }} update-kubeconfig --name ${{ vars.EKS_CLUSTER_NAME }}  
          
      - name: verifying the k8s access
        run: |
          kubectl get nodes 
      
      - name: deploying the helm chart
        run: |
          echo " Deploying the helm chart"
          helm repo add --username ${{ vars.CI_REGISTRY_USER }} --password ${{ secrets.CI_REGISTRY_PASSWORD }} emma-helm ${{ vars.HELM_REPO }}
          helm repo update
          helm upgrade --install airport-acumen-backend emma-helm/tam-aap-backend \
          --namespace default \
          --set image.repository=${{ vars.CI_REGISTRY }}/${{ vars.PROJECT_NAME }}/${{ vars.COMPONENT_NAME }} \
          --set image.tag=${{ needs.build_docker_image.outputs.IMAGE_TAG }} \
          --set fullnameOverride=airport-acumen-backend \
          -n aap-tam

      - name: validating_the_k8s_resources
        run: |
          echo "Waiting for 1 minute ..."
          sleep 60
          pod_status=$(kubectl get pods -n aap-tam | grep 'airport-acumen-backend' | awk '{ print $3 }')
          if [ "$pod_status" != "Running" ]; then
            echo "Pod is not running. Status: $pod_status"
            exit 1 
          fi

      - name: Remove Docker Image
        if: always()
        run: |
          docker rmi ${{ vars.CI_REGISTRY }}/${{ vars.PROJECT_NAME }}/${{ vars.COMPONENT_NAME }}:${{ needs.build_docker_image.outputs.IMAGE_TAG }} || true
